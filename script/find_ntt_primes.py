#!/usr/bin/env python3
"""
NTT友好素数查找脚本
寻找形如 p = k * 2^n + 1 的素数，其中3是原根，支持大规模NTT变换
"""

import math
import random
from typing import List, Tuple, Optional

def miller_rabin_test(n: int, k: int = 10) -> bool:
    """Miller-Rabin素性测试"""
    if n == 2 or n == 3:
        return True
    if n < 2 or n % 2 == 0:
        return False
    
    # 将 n-1 写成 d * 2^r 的形式
    r = 0
    d = n - 1
    while d % 2 == 0:
        r += 1
        d //= 2
    
    # 进行k次测试
    for _ in range(k):
        a = random.randint(2, n - 2)
        x = pow(a, d, n)
        
        if x == 1 or x == n - 1:
            continue
            
        for _ in range(r - 1):
            x = pow(x, 2, n)
            if x == n - 1:
                break
        else:
            return False
    return True

def is_prime(n: int) -> bool:
    """判断一个数是否为素数"""
    if n < 2:
        return False
    if n == 2:
        return True
    if n % 2 == 0:
        return False
    if n < 9:
        return True
    
    # 小素数试除
    small_primes = [3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47]
    for p in small_primes:
        if n == p:
            return True
        if n % p == 0:
            return False
    
    # Miller-Rabin测试
    return miller_rabin_test(n)

def find_primitive_root(p: int, target_root: int = 3) -> bool:
    """检查target_root是否是模p的原根"""
    if not is_prime(p):
        return False
    
    if pow(target_root, p - 1, p) != 1:
        return False
    
    # 检查target_root的阶是否等于p-1
    phi = p - 1
    factors = []
    
    # 分解phi = p-1
    n = phi
    d = 2
    while d * d <= n:
        while n % d == 0:
            factors.append(d)
            n //= d
        d += 1
    if n > 1:
        factors.append(n)
    
    # 去重
    factors = list(set(factors))
    
    # 检查target_root^((p-1)/q) != 1 对所有素因子q
    for q in factors:
        if pow(target_root, phi // q, p) == 1:
            return False
    
    return True

def decompose_ntt_form(p: int) -> Optional[Tuple[int, int]]:
    """
    将数p分解为 k * 2^n + 1 的形式
    返回 (k, n) 如果可以分解，否则返回None
    """
    if p <= 1:
        return None
    
    p_minus_1 = p - 1
    n = 0
    
    # 提取所有的2的因子
    while p_minus_1 % 2 == 0:
        p_minus_1 //= 2
        n += 1
    
    k = p_minus_1
    
    # 验证: k * 2^n + 1 == p
    if k * (1 << n) + 1 == p:
        return (k, n)
    else:
        return None

def find_ntt_primes(max_k: int = 10000, min_n: int = 17, target_root: int = 3) -> List[Tuple[int, int, int]]:
    """
    寻找NTT友好的素数
    返回 [(p, k, n), ...] 其中 p = k * 2^n + 1
    """
    primes = []
    
    print(f"寻找形如 p = k * 2^n + 1 的素数 (k <= {max_k}, n >= {min_n}, 原根={target_root})")
    print("=" * 80)
    
    for n in range(min_n, 30):  # n从min_n到29
        print(f"检查 n = {n} (支持最大变换长度: 2^{n} = {1 << n})")
        
        power_of_2 = 1 << n
        
        for k in range(1, max_k + 1, 2):  # k必须是奇数
            p = k * power_of_2 + 1
            
            if p > (1 << 31):  # 避免过大的数
                break
            
            if is_prime(p):
                if find_primitive_root(p, target_root):
                    primes.append((p, k, n))
                    print(f"  找到: p = {p} = {k} * 2^{n} + 1 (原根 {target_root} 存在)")
                    
                    # 验证分解
                    decomp = decompose_ntt_form(p)
                    if decomp and decomp == (k, n):
                        print(f"    验证通过: {p} = {decomp[0]} * 2^{decomp[1]} + 1")
                    else:
                        print(f"    警告: 分解验证失败!")
    
    return primes

def analyze_existing_primes():
    """分析现有的已知NTT素数"""
    known_primes = [
        998244353,   # 119 * 2^23 + 1
        1004535809,  # 479 * 2^21 + 1
        469762049,   # 7 * 2^26 + 1
        # 其他已知的NTT友好素数
        2013265921,  # 15 * 2^27 + 1
        1811939329,  # 13 * 2^27 + 1
        2281701377,  # 17 * 2^27 + 1
        3221225473,  # 3 * 2^30 + 1
        5767168001,  # 11 * 2^29 + 1
    ]
    
    print("分析已知的NTT友好素数:")
    print("=" * 60)
    
    for p in known_primes:
        if is_prime(p):
            decomp = decompose_ntt_form(p)
            has_root_3 = find_primitive_root(p, 3)
            
            print(f"p = {p}")
            if decomp:
                k, n = decomp
                max_ntt_size = 1 << n
                print(f"  分解: {k} * 2^{n} + 1")
                print(f"  最大NTT长度: 2^{n} = {max_ntt_size}")
            else:
                print(f"  不是标准NTT形式")
            
            print(f"  原根3存在: {'是' if has_root_3 else '否'}")
            
            # 检查其他可能的原根
            for root in [2, 5, 7, 10]:
                if find_primitive_root(p, root):
                    print(f"  原根{root}存在: 是")
            print()

def generate_small_primes():
    """生成一些较小的NTT友好素数供测试使用"""
    print("生成小规模测试用的NTT友好素数:")
    print("=" * 50)
    
    small_primes = find_ntt_primes(max_k=100, min_n=10, target_root=3)
    
    print(f"\n找到 {len(small_primes)} 个小规模NTT友好素数:")
    for p, k, n in small_primes[:10]:  # 只显示前10个
        print(f"  {p} = {k} * 2^{n} + 1 (最大NTT: 2^{n})")

def main():
    print("NTT友好素数查找器")
    print("=" * 80)
    
    # 分析现有素数
    analyze_existing_primes()
    
    print("\n")
    
    # 生成小规模测试素数
    generate_small_primes()
    
    print("\n")
    
    # 寻找新的大规模素数
    print("寻找新的大规模NTT友好素数:")
    print("=" * 50)
    
    new_primes = find_ntt_primes(max_k=2000, min_n=20, target_root=3)
    
    print(f"\n总共找到 {len(new_primes)} 个新的大规模NTT友好素数:")
    print("推荐用于替换或补充现有三模系统的素数:")
    
    # 按支持的最大变换长度排序
    new_primes.sort(key=lambda x: x[2], reverse=True)
    
    for i, (p, k, n) in enumerate(new_primes[:10]):
        max_transform = 1 << n
        print(f"{i+1:2d}. p = {p:>10} = {k:>4} * 2^{n:>2} + 1  (最大NTT: 2^{n:>2} = {max_transform:>8})")
    
    # 生成C++代码
    if new_primes:
        print(f"\nC++代码片段（前5个新素数）:")
        print("constexpr uint32_t new_ntt_primes[] = {")
        for p, k, n in new_primes[:5]:
            print(f"    {p}u,  // {k} * 2^{n} + 1, max_ntt_size = 2^{n}")
        print("};")

if __name__ == "__main__":
    main() 