#!/usr/bin/env python3
"""
快速NTT友好素数查找器
专注于寻找适合32位整数范围且原根为3的素数
"""

def is_prime_fast(n):
    """快速素性检测（适用于32位范围）"""
    if n < 2:
        return False
    if n == 2 or n == 3:
        return True
    if n % 2 == 0 or n % 3 == 0:
        return False
    
    i = 5
    while i * i <= n:
        if n % i == 0 or n % (i + 2) == 0:
            return False
        i += 6
    return True

def mod_pow(base, exp, mod):
    """快速模幂"""
    result = 1
    base %= mod
    while exp > 0:
        if exp & 1:
            result = (result * base) % mod
        base = (base * base) % mod
        exp >>= 1
    return result

def is_primitive_root_3(p):
    """检查3是否是素数p的原根"""
    if not is_prime_fast(p):
        return False
    
    if mod_pow(3, p - 1, p) != 1:
        return False
    
    # 对于形如 k * 2^n + 1 的素数，快速检查
    phi = p - 1
    
    # 找到所有phi的素因子
    factors = []
    temp = phi
    d = 2
    while d * d <= temp:
        if temp % d == 0:
            factors.append(d)
            while temp % d == 0:
                temp //= d
        d += 1
    if temp > 1:
        factors.append(temp)
    
    # 检查3^(phi/q) != 1 对所有素因子q
    for q in factors:
        if mod_pow(3, phi // q, p) == 1:
            return False
    
    return True

def find_suitable_ntt_primes():
    """寻找适合的NTT友好素数"""
    print("寻找适合32位系统的NTT友好素数（原根=3）")
    print("=" * 60)
    
    # 现有的已知好用的素数
    known_primes = [
        (998244353, 119, 23),   # 当前使用
        (1004535809, 479, 21),  # 当前使用
        (469762049, 7, 26),     # 当前使用
        (2281701377, 17, 27),   # 新发现的好选择
    ]
    
    print("已验证的NTT友好素数（原根=3）:")
    for p, k, n in known_primes:
        if is_primitive_root_3(p):
            max_ntt = 1 << n
            print(f"  {p:>10} = {k:>3} * 2^{n:>2} + 1  (最大NTT: 2^{n:>2} = {max_ntt:>8})")
        else:
            print(f"  {p:>10} = {k:>3} * 2^{n:>2} + 1  (原根3不存在!)")
    
    print("\n寻找新的NTT友好素数:")
    print("-" * 40)
    
    new_primes = []
    
    # 搜索n=20到28的范围（支持1M到256M的NTT）
    for n in range(20, 29):
        power_2_n = 1 << n
        max_k = min(2000, (1 << 31) // power_2_n)  # 确保在32位范围内
        
        print(f"n={n:2d} (2^{n} = {power_2_n:>9}): ", end="", flush=True)
        count = 0
        
        for k in range(1, max_k + 1, 2):  # k必须是奇数
            p = k * power_2_n + 1
            
            if p > (1 << 31) - 1:  # 32位有符号整数最大值
                break
                
            if is_prime_fast(p) and is_primitive_root_3(p):
                new_primes.append((p, k, n))
                count += 1
                if count <= 3:  # 只显示前3个
                    print(f"{p}({k})", end=" ")
        
        print(f" -> 共{count}个")
    
    print(f"\n推荐的新NTT友好素数组合:")
    print("=" * 50)
    
    # 选择不同n值的最佳候选
    best_candidates = {}
    for p, k, n in new_primes:
        if n not in best_candidates or k < best_candidates[n][1]:
            best_candidates[n] = (p, k, n)
    
    # 按n值排序显示
    for n in sorted(best_candidates.keys(), reverse=True):
        p, k, n = best_candidates[n]
        max_ntt = 1 << n
        print(f"  {p:>10} = {k:>3} * 2^{n:>2} + 1  (最大NTT: 2^{n:>2} = {max_ntt:>8})")
    
    # 生成替代三模组合的建议
    print(f"\n替代当前三模组合的建议:")
    print("-" * 40)
    
    # 方案1: 保持高性能，选择较大的n值
    if 26 in best_candidates and 25 in best_candidates and 24 in best_candidates:
        combo1 = [best_candidates[26], best_candidates[25], best_candidates[24]]
        print("方案1 (高性能): ")
        for p, k, n in combo1:
            print(f"  {p}u,  // {k} * 2^{n} + 1")
    
    # 方案2: 平衡性能和精度
    if 24 in best_candidates and 23 in best_candidates and 22 in best_candidates:
        combo2 = [best_candidates[24], best_candidates[23], best_candidates[22]]
        print("方案2 (平衡): ")
        for p, k, n in combo2:
            print(f"  {p}u,  // {k} * 2^{n} + 1")
    
    # 显示当前三模的乘积范围
    current_product = 998244353 * 1004535809 * 469762049
    print(f"\n当前三模乘积: {current_product:.2e}")
    
    # 计算新组合的乘积
    if 26 in best_candidates and 25 in best_candidates and 24 in best_candidates:
        new_product = best_candidates[26][0] * best_candidates[25][0] * best_candidates[24][0]
        print(f"新方案1乘积: {new_product:.2e}")
        print(f"精度提升: {new_product / current_product:.2f}倍")

def test_current_primes():
    """测试当前使用的三个素数"""
    current_primes = [998244353, 1004535809, 469762049]
    
    print("验证当前使用的三个素数:")
    print("-" * 30)
    
    all_good = True
    for p in current_primes:
        is_prime = is_prime_fast(p)
        has_root_3 = is_primitive_root_3(p)
        
        print(f"{p}: 素数={is_prime}, 原根3={has_root_3}")
        
        if not (is_prime and has_root_3):
            all_good = False
    
    if all_good:
        print("✓ 所有素数都符合要求")
    else:
        print("✗ 发现问题!")
    
    return all_good

if __name__ == "__main__":
    # 首先验证当前素数
    test_current_primes()
    print()
    
    # 然后寻找新的素数
    find_suitable_ntt_primes() 