#!/usr/bin/env python3

def is_prime(n):
    if n < 2: return False
    if n == 2: return True
    if n % 2 == 0: return False
    for i in range(3, int(n**0.5) + 1, 2):
        if n % i == 0: return False
    return True

def mod_pow(a, b, m):
    res = 1
    a %= m
    while b > 0:
        if b & 1: res = (res * a) % m
        a = (a * a) % m
        b >>= 1
    return res

def check_primitive_root_3(p):
    if not is_prime(p): return False
    if mod_pow(3, p - 1, p) != 1: return False
    
    phi = p - 1
    factors = []
    n = phi
    for d in range(2, int(n**0.5) + 1):
        if n % d == 0:
            factors.append(d)
            while n % d == 0: n //= d
    if n > 1: factors.append(n)
    
    for q in factors:
        if mod_pow(3, phi // q, p) == 1:
            return False
    return True

print("可用的NTT友好素数 (原根=3):")
print("=" * 50)

# 当前使用的三个模数
current = [998244353, 1004535809, 469762049]
print("当前使用:")
for p in current:
    if check_primitive_root_3(p):
        p_minus_1 = p - 1
        n = 0
        while p_minus_1 % 2 == 0:
            p_minus_1 //= 2
            n += 1
        k = p_minus_1
        print(f"  {p} = {k} * 2^{n} + 1")

print("\n其他推荐素数:")

# 一些已知的好素数
candidates = [
    7340033,    # 7 * 2^20 + 1
    23068673,   # 11 * 2^21 + 1
    104857601,  # 25 * 2^22 + 1
    595591169,  # 71 * 2^23 + 1
    1224736769, # 73 * 2^24 + 1
    167772161,  # 5 * 2^25 + 1
    2281701377, # 17 * 2^27 + 1
]

for p in candidates:
    if check_primitive_root_3(p):
        p_minus_1 = p - 1
        n = 0
        while p_minus_1 % 2 == 0:
            p_minus_1 //= 2
            n += 1
        k = p_minus_1
        max_ntt = 1 << n
        print(f"  {p} = {k} * 2^{n} + 1  (最大NTT: 2^{n} = {max_ntt})")

print("\n推荐的新三模组合:")
print("-" * 30)
print("组合1 (高精度):")
print("  998244353u,  // 119 * 2^23 + 1")
print("  2281701377u,  // 17 * 2^27 + 1") 
print("  1224736769u,  // 73 * 2^24 + 1")

print("\n组合2 (平衡):")
print("  469762049u,   // 7 * 2^26 + 1")
print("  595591169u,   // 71 * 2^23 + 1")
print("  104857601u,   // 25 * 2^22 + 1")

print("\n组合3 (兼容性):")
print("  998244353u,   // 119 * 2^23 + 1  (保持现有)")
print("  1004535809u,  // 479 * 2^21 + 1  (保持现有)")
print("  2281701377u,  // 17 * 2^27 + 1   (新增，更高精度)") 