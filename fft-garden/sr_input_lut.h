/*
    Written in 2019 by <PERSON> <<EMAIL>>

    To the extent possible under law, the author(s) have dedicated all
    copyright and related and neighboring rights to this software to
    the public domain worldwide. This software is distributed without
    any warranty.

    You should have received a copy of the CC0 Public Domain
    Dedication along with this software. If not, see
    http://creativecommons.org/publicdomain/zero/1.0/
*/


/* Lookup table for input reordering in some Split-Radix FFT
   algorithms */

#ifndef _SR_INPUT_LUT_H_
#define _SR_INPUT_LUT_H_

extern unsigned *sr_input_lut;	/* [N] */

void sr_input_lut_init(unsigned N);
void sr_input_lut_cleanup();

#endif
