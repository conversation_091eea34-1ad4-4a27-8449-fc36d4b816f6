/**
 * @file ntt_bit_reverse.h
 * @brief NTT 位反转操作定义
 * @details 提供高效的位反转排列函数用于 NTT 预处理
 */

#ifndef _NTT_BIT_REVERSE_H_
#define _NTT_BIT_REVERSE_H_

#include <stdint.h>

/**
 * @brief 计算整数的位反转值
 * @param x 输入值
 * @param bits 位数
 * @return 位反转后的值
 */
static inline unsigned bit_reverse(unsigned x, unsigned bits)
{
    unsigned result = 0;
    for (unsigned i = 0; i < bits; i++) {
        result = (result << 1) | (x & 1);
        x >>= 1;
    }
    return result;
}

/**
 * @brief 位反转循环宏，用于高效的位反转数据重排
 * @param i 循环变量
 * @param j 反转后的索引
 * @param n 数组长度
 * @param body 循环体代码
 */
#define NTT_BIT_REVERSE_LOOP(i, j, n, body)  \
    do {                                      \
        unsigned log2_n = 31 - clz32(n);      \
        for (unsigned i = 0; i < n; i++) {    \
            unsigned j = bit_reverse(i, log2_n); \
            body                              \
        }                                     \
    } while(0)

#endif 