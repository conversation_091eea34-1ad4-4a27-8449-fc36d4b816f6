/**
 * @file ntt_twiddles.h
 * @brief NTT 旋转因子生成和管理
 * @details 为 Split-Radix NTT 生成和缓存旋转因子
 */

#ifndef _NTT_TWIDDLES_H_
#define _NTT_TWIDDLES_H_

#include "ntt.h"

/**
 * @brief 初始化 NTT 旋转因子表
 * @param length 旋转因子表长度
 * @param n 变换长度
 * @param config NTT 配置参数
 */
void ntt_twiddles_init(unsigned length, unsigned n, const NttConfig *config);

/**
 * @brief 初始化 Split-Radix NTT 专用旋转因子表
 * @param length 旋转因子表长度
 * @param n 变换长度  
 * @param config NTT 配置参数
 */
void ntt_twiddles_sr_init(unsigned length, unsigned n, const NttConfig *config);

/**
 * @brief 清理旋转因子表
 */
void ntt_twiddles_cleanup(void);

/**
 * @brief 获取旋转因子 w^i
 * @param i 索引
 * @param log2_n 变换长度的对数
 * @return 旋转因子值
 */
uint64_t get_ntt_twiddle(unsigned i, unsigned log2_n);

/**
 * @brief 获取 Split-Radix 专用旋转因子 w^(3*i)
 * @param i 索引
 * @param log2_n 变换长度的对数
 * @return 旋转因子值
 */
uint64_t get_ntt_twiddle_sr(unsigned i, unsigned log2_n);

extern uint64_t *ntt_twiddles;
extern uint64_t *ntt_twiddles_sr;
extern uint64_t ntt_modulus;

#endif 