# Makefile for Split-Radix NTT
# 用于编译和测试 Split-Radix NTT 实现

CC = gcc
CFLAGS = -Wall -Wextra -O3 -std=c99 -ffast-math
LDFLAGS = -lm

# NTT 源文件 (使用正确的 Split-Radix 实现)
NTT_SRCS = ntt_modular.c ntt_split_radix_corrected.c
NTT_OBJS = $(NTT_SRCS:.c=.o)
NTT_HEADERS = ntt.h ntt_bit_reverse.h

# 测试程序
TEST_TARGET = ntt_test
TEST_SRCS = ntt_test.c

# 默认目标
all: $(TEST_TARGET)

# 编译测试程序
$(TEST_TARGET): $(NTT_OBJS) ntt_test.o
	$(CC) $(CFLAGS) -o $@ $^ $(LDFLAGS)

# 编译目标文件
%.o: %.c $(NTT_HEADERS)
	$(CC) $(CFLAGS) -c -o $@ $<

# 运行测试
test: $(TEST_TARGET)
	./$(TEST_TARGET)

# 清理
clean:
	rm -f *.o $(TEST_TARGET)

# 调试版本
debug: CFLAGS += -g -DDEBUG
debug: $(TEST_TARGET)

# 性能分析版本
profile: CFLAGS += -pg
profile: $(TEST_TARGET)

# 查看汇编代码
asm: ntt_split_radix_corrected.s

%.s: %.c $(NTT_HEADERS)
	$(CC) $(CFLAGS) -S -o $@ $<

.PHONY: all test clean debug profile asm 