/**
 * @file ntt_twiddles.c  
 * @brief NTT 旋转因子生成和管理实现
 * @details 实现基于本原根的旋转因子计算和缓存机制
 */

#include "ntt_twiddles.h"
#include <stdlib.h>
#include <assert.h>

uint64_t *ntt_twiddles = NULL;
uint64_t *ntt_twiddles_sr = NULL;
uint64_t ntt_modulus = 0;
static uint64_t ntt_root = 0;

void ntt_twiddles_init(unsigned length, unsigned n, const NttConfig *config)
{
    assert(config != NULL);
    assert(length > 0);
    
    ntt_modulus = config->modulus;
    ntt_root = config->root;
    
    ntt_twiddles = malloc(length * sizeof(uint64_t));
    assert(ntt_twiddles != NULL);
    
    uint64_t w = mod_pow(ntt_root, (config->modulus - 1) / n, config->modulus);
    
    ntt_twiddles[0] = 1;
    for (unsigned i = 1; i < length; i++) {
        ntt_twiddles[i] = mod_mul(ntt_twiddles[i - 1], w, config->modulus);
    }
}

void ntt_twiddles_sr_init(unsigned length, unsigned n, const NttConfig *config)
{
    assert(config != NULL);
    assert(length > 0);
    
    ntt_twiddles_sr = malloc(length * sizeof(uint64_t));
    assert(ntt_twiddles_sr != NULL);
    
    uint64_t w = mod_pow(ntt_root, (config->modulus - 1) / n, config->modulus);
    uint64_t w3 = mod_mul(mod_mul(w, w, config->modulus), w, config->modulus);
    
    ntt_twiddles_sr[0] = 1;
    for (unsigned i = 1; i < length; i++) {
        ntt_twiddles_sr[i] = mod_mul(ntt_twiddles_sr[i - 1], w3, config->modulus);
    }
}

void ntt_twiddles_cleanup(void)
{
    if (ntt_twiddles) {
        free(ntt_twiddles);
        ntt_twiddles = NULL;
    }
    if (ntt_twiddles_sr) {
        free(ntt_twiddles_sr);
        ntt_twiddles_sr = NULL;
    }
    ntt_modulus = 0;
    ntt_root = 0;
}

uint64_t get_ntt_twiddle(unsigned i, unsigned log2_n)
{
    assert(ntt_twiddles != NULL);
    assert(i < (1U << log2_n));
    return ntt_twiddles[i];
}

uint64_t get_ntt_twiddle_sr(unsigned i, unsigned log2_n)
{
    assert(ntt_twiddles_sr != NULL);
    assert(i < (1U << log2_n));
    return ntt_twiddles_sr[i];
} 