/**
 * @file ntt_test.c
 * @brief Split-Radix NTT 测试程序
 * @details 验证 NTT 正向和逆向变换的正确性
 */

#include "ntt.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <assert.h>

void print_array(const char *name, const uint64_t *arr, unsigned n)
{
    printf("%s: [", name);
    for (unsigned i = 0; i < n; i++) {
        printf("%lu", arr[i]);
        if (i < n - 1) printf(", ");
    }
    printf("]\n");
}

int test_ntt_basic(void)
{
    const unsigned n = 8;
    const NttConfig config = {
        .modulus = 998244353,
        .root = 3,
        .max_log2_n = 20
    };
    
    printf("=== 基本 NTT 测试 (n=%u, mod=%lu) ===\n", n, config.modulus);
    
    ntt_init(&config, n);
    
    uint64_t input[8] = {1, 2, 3, 4, 5, 6, 7, 8};
    uint64_t forward[8], inverse[8];
    
    print_array("输入", input, n);
    
    ntt_forward(n, forward, input);
    print_array("正向NTT", forward, n);
    
    ntt_inverse(n, inverse, forward);
    print_array("逆向NTT", inverse, n);
    
    int success = 1;
    for (unsigned i = 0; i < n; i++) {
        if (input[i] != inverse[i]) {
            printf("错误: input[%u]=%lu != inverse[%u]=%lu\n", 
                   i, input[i], i, inverse[i]);
            success = 0;
        }
    }
    
    ntt_cleanup();
    
    if (success) {
        printf("✓ 基本测试通过\n\n");
    } else {
        printf("✗ 基本测试失败\n\n");
    }
    
    return success;
}

int test_ntt_convolution(void)
{
    const unsigned n = 8;
    const NttConfig config = {
        .modulus = 998244353,
        .root = 3,
        .max_log2_n = 20
    };
    
    printf("=== NTT 卷积测试 (n=%u) ===\n", n);
    
    ntt_init(&config, n);
    
    uint64_t a[8] = {1, 2, 3, 4, 0, 0, 0, 0};
    uint64_t b[8] = {1, 1, 1, 1, 0, 0, 0, 0};
    uint64_t a_ntt[8], b_ntt[8], c_ntt[8], c[8];
    
    print_array("多项式 A", a, n);
    print_array("多项式 B", b, n);
    
    ntt_forward(n, a_ntt, a);
    ntt_forward(n, b_ntt, b);
    
    for (unsigned i = 0; i < n; i++) {
        c_ntt[i] = mod_mul(a_ntt[i], b_ntt[i], config.modulus);
    }
    
    ntt_inverse(n, c, c_ntt);
    
    print_array("卷积结果", c, n);
    
    uint64_t expected[8] = {1, 3, 6, 10, 9, 7, 4, 0};
    
    int success = 1;
    for (unsigned i = 0; i < n; i++) {
        if (c[i] != expected[i]) {
            printf("错误: c[%u]=%lu != expected[%u]=%lu\n", 
                   i, c[i], i, expected[i]);
            success = 0;
        }
    }
    
    ntt_cleanup();
    
    if (success) {
        printf("✓ 卷积测试通过\n\n");
    } else {
        printf("✗ 卷积测试失败\n\n");
    }
    
    return success;
}

int test_ntt_performance(void)
{
    const unsigned n = 1024;
    const NttConfig config = {
        .modulus = 998244353,
        .root = 3,
        .max_log2_n = 20
    };
    
    printf("=== NTT 性能测试 (n=%u) ===\n", n);
    
    ntt_init(&config, n);
    
    uint64_t *input = malloc(n * sizeof(uint64_t));
    uint64_t *output = malloc(n * sizeof(uint64_t));
    
    srand(time(NULL));
    for (unsigned i = 0; i < n; i++) {
        input[i] = rand() % config.modulus;
    }
    
    clock_t start = clock();
    const int iterations = 1000;
    
    for (int i = 0; i < iterations; i++) {
        ntt_forward(n, output, input);
    }
    
    clock_t end = clock();
    double time_taken = ((double)(end - start)) / CLOCKS_PER_SEC;
    
    printf("执行 %d 次正向 NTT 耗时: %.6f 秒\n", iterations, time_taken);
    printf("平均每次耗时: %.6f 毫秒\n", (time_taken * 1000) / iterations);
    
    free(input);
    free(output);
    ntt_cleanup();
    
    printf("✓ 性能测试完成\n\n");
    return 1;
}

int main(void)
{
    printf("Split-Radix NTT 测试程序\n");
    printf("========================\n\n");
    
    int test1 = test_ntt_basic();
    int test2 = test_ntt_convolution();
    int test3 = test_ntt_performance();
    
    if (test1 && test2 && test3) {
        printf("🎉 所有测试通过！\n");
        return 0;
    } else {
        printf("❌ 某些测试失败\n");
        return 1;
    }
} 