/**
 * @file ntt_modular.c
 * @brief NTT 模运算基础函数实现
 * @details 提供高效的模乘法、模幂和模逆元运算
 */

#include "ntt.h"
#include <assert.h>

uint64_t mod_mul(uint64_t a, uint64_t b, uint64_t mod)
{
    return (((__uint128_t)a * b) % mod);
}

uint64_t mod_pow(uint64_t base, uint64_t exp, uint64_t mod)
{
    uint64_t result = 1;
    base %= mod;
    while (exp > 0) {
        if (exp & 1) {
            result = mod_mul(result, base, mod);
        }
        base = mod_mul(base, base, mod);
        exp >>= 1;
    }
    return result;
}

static uint64_t extended_gcd(uint64_t a, uint64_t b, int64_t *x, int64_t *y)
{
    if (a == 0) {
        *x = 0;
        *y = 1;
        return b;
    }
    int64_t x1, y1;
    uint64_t gcd = extended_gcd(b % a, a, &x1, &y1);
    *x = y1 - (b / a) * x1;
    *y = x1;
    return gcd;
}

uint64_t mod_inverse(uint64_t a, uint64_t mod)
{
    int64_t x, y;
    uint64_t gcd = extended_gcd(a % mod, mod, &x, &y);
    assert(gcd == 1);
    return (x % (int64_t)mod + mod) % mod;
} 