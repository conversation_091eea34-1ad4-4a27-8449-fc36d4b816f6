/**
 * @file ntt_split_radix.c
 * @brief Split-Radix NTT 串行实现
 * @details 基于 Burrus84 Split-Radix 算法的数论变换实现
 */

#include "ntt.h"
#include "ntt_twiddles.h"
#include "ntt_bf.h"
#include "ntt_bit_reverse.h"
#include <stdlib.h>
#include <assert.h>

static NttConfig current_config;
static uint64_t *inv_twiddles = NULL;
static uint64_t *inv_twiddles_sr = NULL;

static void burrus84_ntt(uint64_t * restrict out, const uint64_t * restrict in, unsigned n, unsigned m)
{
    NTT_BIT_REVERSE_LOOP(i, j, n, {
        out[i] = in[j];
    });
    
    for (unsigned is = 0, id = 4; is < n; ) {
        for (unsigned i0 = is; i0 < n; i0 += id) {
            ntt_ct_dit_bf2_0(1, 1, out + i0, out + i0);
        }
        is = 2 * id - 2;
        id = 4 * id;
    }
    
    if (m < 2) return;
    
    for (unsigned k = 2; k <= m; k++) {
        unsigned n2 = 1 << k;
        unsigned n4 = n2 >> 2;
        
        for (unsigned j = 0; j < n4; j++) {
            unsigned t = j << (m - k);
            uint64_t w1 = get_ntt_twiddle(t, m);
            uint64_t w3 = get_ntt_twiddle_sr(t, m);
            
            for (unsigned is = j, id = 2 << k; is < n; ) {
                for (unsigned i0 = is; i0 < n; i0 += id) {
                    ntt_sr_dit_bf4(n4, out + i0, w1, w3);
                }
                is = 2 * id - n2 + j;
                id = 4 * id;
            }
        }
    }
}

static void init_inverse_twiddles(unsigned n)
{
    if (inv_twiddles) return;
    
    inv_twiddles = malloc(n * sizeof(uint64_t));
    inv_twiddles_sr = malloc((n/4) * sizeof(uint64_t));
    assert(inv_twiddles && inv_twiddles_sr);
    
    uint64_t w_inv = mod_pow(current_config.root, current_config.modulus - 1 - (current_config.modulus - 1) / n, current_config.modulus);
    uint64_t w3_inv = mod_mul(mod_mul(w_inv, w_inv, current_config.modulus), w_inv, current_config.modulus);
    
    inv_twiddles[0] = 1;
    for (unsigned i = 1; i < n; i++) {
        inv_twiddles[i] = mod_mul(inv_twiddles[i - 1], w_inv, current_config.modulus);
    }
    
    inv_twiddles_sr[0] = 1;
    for (unsigned i = 1; i < n/4; i++) {
        inv_twiddles_sr[i] = mod_mul(inv_twiddles_sr[i - 1], w3_inv, current_config.modulus);
    }
}

static void burrus84_intt(uint64_t * restrict out, const uint64_t * restrict in, unsigned n, unsigned m)
{
    init_inverse_twiddles(n);
    
    NTT_BIT_REVERSE_LOOP(i, j, n, {
        out[i] = in[j];
    });
    
    for (unsigned is = 0, id = 4; is < n; ) {
        for (unsigned i0 = is; i0 < n; i0 += id) {
            ntt_ct_dit_bf2_0(1, 1, out + i0, out + i0);
        }
        is = 2 * id - 2;
        id = 4 * id;
    }
    
    if (m < 2) return;
    
    for (unsigned k = 2; k <= m; k++) {
        unsigned n2 = 1 << k;
        unsigned n4 = n2 >> 2;
        
        for (unsigned j = 0; j < n4; j++) {
            unsigned t = j << (m - k);
            uint64_t w1_inv = inv_twiddles[t];
            uint64_t w3_inv = inv_twiddles_sr[t];
            
            for (unsigned is = j, id = 2 << k; is < n; ) {
                for (unsigned i0 = is; i0 < n; i0 += id) {
                    ntt_sr_dit_bf4(n4, out + i0, w1_inv, w3_inv);
                }
                is = 2 * id - n2 + j;
                id = 4 * id;
            }
        }
    }
    
    uint64_t n_inv = mod_inverse(n, current_config.modulus);
    for (unsigned i = 0; i < n; i++) {
        out[i] = mod_mul(out[i], n_inv, current_config.modulus);
    }
}

void ntt_init(const NttConfig *config, unsigned n)
{
    assert(config != NULL);
    assert((n & (n - 1)) == 0);
    
    current_config = *config;
    ntt_twiddles_init(n, n, config);
    ntt_twiddles_sr_init(n / 4, n, config);
}

void ntt_cleanup(void)
{
    ntt_twiddles_cleanup();
    if (inv_twiddles) {
        free(inv_twiddles);
        inv_twiddles = NULL;
    }
    if (inv_twiddles_sr) {
        free(inv_twiddles_sr);
        inv_twiddles_sr = NULL;
    }
}

void ntt_forward(unsigned n, uint64_t * restrict out, const uint64_t * restrict in)
{
    assert(out != NULL && in != NULL);
    assert((n & (n - 1)) == 0);
    
    unsigned log2_n = 31 - clz32(n);
    burrus84_ntt(out, in, n, log2_n);
}

void ntt_inverse(unsigned n, uint64_t * restrict out, const uint64_t * restrict in)
{
    assert(out != NULL && in != NULL);
    assert((n & (n - 1)) == 0);
    
    unsigned log2_n = 31 - clz32(n);
    burrus84_intt(out, in, n, log2_n);
} 