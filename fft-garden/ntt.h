/**
 * @file ntt.h
 * @brief Number Theoretic Transform (NTT) 接口定义
 * @details 基于 Split-Radix 算法的 NTT 实现，用于模运算下的快速数论变换
 */

#ifndef _NTT_H_
#define _NTT_H_

#include <stdint.h>

/**
 * @brief NTT 配置参数结构体
 * @param modulus 模数，必须为素数且满足 modulus = k*2^m + 1
 * @param root 本原根，满足 root^((modulus-1)/2) ≡ -1 (mod modulus)  
 * @param max_log2_n 支持的最大变换长度的对数值
 */
typedef struct {
    uint64_t modulus;
    uint64_t root;
    unsigned max_log2_n;
} NttConfig;

/**
 * @brief 初始化 NTT 系统
 * @param config NTT 配置参数
 * @param n 变换长度，必须为 2 的幂次
 */
void ntt_init(const NttConfig *config, unsigned n);

/**
 * @brief 清理 NTT 系统资源
 */
void ntt_cleanup(void);

/**
 * @brief 正向 NTT 变换 (DIT Split-Radix)
 * @param n 变换长度
 * @param out 输出数组
 * @param in 输入数组
 */
void ntt_forward(unsigned n, uint64_t * restrict out, const uint64_t * restrict in);

/**
 * @brief 逆向 NTT 变换
 * @param n 变换长度  
 * @param out 输出数组
 * @param in 输入数组
 */
void ntt_inverse(unsigned n, uint64_t * restrict out, const uint64_t * restrict in);

/**
 * @brief 模乘法运算
 * @param a 操作数1
 * @param b 操作数2
 * @param mod 模数
 * @return (a * b) % mod
 */
uint64_t mod_mul(uint64_t a, uint64_t b, uint64_t mod);

/**
 * @brief 模幂运算
 * @param base 底数
 * @param exp 指数
 * @param mod 模数  
 * @return base^exp % mod
 */
uint64_t mod_pow(uint64_t base, uint64_t exp, uint64_t mod);

/**
 * @brief 模逆元计算
 * @param a 数值
 * @param mod 模数
 * @return a 在模 mod 下的逆元
 */
uint64_t mod_inverse(uint64_t a, uint64_t mod);

#define clz32(x) __builtin_clz(x)

#endif 