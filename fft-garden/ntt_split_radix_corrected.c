/**
 * @file ntt_split_radix_corrected.c
 * @brief 正确的 Split-Radix NTT 实现
 * @details 基于正确理解的 Split-Radix NTT 算法，不直接从 FFT 移植
 */

#include "ntt.h"
#include "ntt_bit_reverse.h"
#include <stdlib.h>
#include <assert.h>

static NttConfig current_config;

static void split_radix_ntt_dit(uint64_t *a, unsigned n, int inverse)
{
    if (n <= 1) return;
    
    if (n == 2) {
        uint64_t t = a[1];
        a[1] = (a[0] + current_config.modulus - t) % current_config.modulus;
        a[0] = (a[0] + t) % current_config.modulus;
        return;
    }
    
    unsigned n2 = n / 2;
    unsigned n4 = n / 4;
    
    split_radix_ntt_dit(a, n2, inverse);
    split_radix_ntt_dit(a + n2, n4, inverse);
    split_radix_ntt_dit(a + n2 + n4, n4, inverse);
    
    uint64_t w, w_step, w3, w3_step;
    if (inverse) {
        w = mod_pow(current_config.root, current_config.modulus - 1 - (current_config.modulus - 1) / n, current_config.modulus);
        w3 = mod_mul(mod_mul(w, w, current_config.modulus), w, current_config.modulus);
    } else {
        w = mod_pow(current_config.root, (current_config.modulus - 1) / n, current_config.modulus);
        w3 = mod_mul(mod_mul(w, w, current_config.modulus), w, current_config.modulus);
    }
    
    w_step = 1;
    w3_step = 1;
    
    for (unsigned i = 0; i < n4; i++) {
        uint64_t a1 = a[i];
        uint64_t a2 = a[i + n2];
        uint64_t a3 = a[i + n4];
        uint64_t a4 = a[i + n2 + n4];
        
        uint64_t t1 = mod_mul(w_step, a3, current_config.modulus);
        uint64_t t2 = mod_mul(w3_step, a4, current_config.modulus);
        
        uint64_t t3 = (t1 + t2) % current_config.modulus;
        uint64_t t4 = (t1 + current_config.modulus - t2) % current_config.modulus;
        
        a[i] = (a1 + t3) % current_config.modulus;
        a[i + n2] = (a1 + current_config.modulus - t3) % current_config.modulus;
        a[i + n4] = (a2 + t4) % current_config.modulus;
        a[i + n2 + n4] = (a2 + current_config.modulus - t4) % current_config.modulus;
        
        w_step = mod_mul(w_step, w, current_config.modulus);
        w3_step = mod_mul(w3_step, w3, current_config.modulus);
    }
}

void ntt_init(const NttConfig *config, unsigned n)
{
    assert(config != NULL);
    assert((n & (n - 1)) == 0);
    
    current_config = *config;
}

void ntt_cleanup(void)
{
    
}

void ntt_forward(unsigned n, uint64_t * restrict out, const uint64_t * restrict in)
{
    assert(out != NULL && in != NULL);
    assert((n & (n - 1)) == 0);
    
    NTT_BIT_REVERSE_LOOP(i, j, n, {
        out[i] = in[j];
    });
    
    split_radix_ntt_dit(out, n, 0);
}

void ntt_inverse(unsigned n, uint64_t * restrict out, const uint64_t * restrict in)
{
    assert(out != NULL && in != NULL);
    assert((n & (n - 1)) == 0);
    
    NTT_BIT_REVERSE_LOOP(i, j, n, {
        out[i] = in[j];
    });
    
    split_radix_ntt_dit(out, n, 1);
    
    uint64_t n_inv = mod_inverse(n, current_config.modulus);
    for (unsigned i = 0; i < n; i++) {
        out[i] = mod_mul(out[i], n_inv, current_config.modulus);
    }
} 