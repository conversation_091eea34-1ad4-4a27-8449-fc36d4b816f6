/**
 * @file ntt_split_radix_fixed.c
 * @brief 修复后的 Split-Radix NTT 串行实现
 * @details 基于 Burrus84 Split-Radix 算法的正确数论变换实现
 */

#include "ntt.h"
#include "ntt_bit_reverse.h"
#include "ntt_bf_fixed.h"
#include <stdlib.h>
#include <assert.h>

static NttConfig current_config;
uint64_t ntt_modulus_global = 0;

static void generate_twiddles(uint64_t **twiddles, uint64_t **twiddles_sr, unsigned n)
{
    *twiddles = malloc(n * sizeof(uint64_t));
    *twiddles_sr = malloc((n/4) * sizeof(uint64_t));
    assert(*twiddles && *twiddles_sr);
    
    uint64_t w = mod_pow(current_config.root, (current_config.modulus - 1) / n, current_config.modulus);
    uint64_t w3 = mod_mul(mod_mul(w, w, current_config.modulus), w, current_config.modulus);
    
    (*twiddles)[0] = 1;
    for (unsigned i = 1; i < n; i++) {
        (*twiddles)[i] = mod_mul((*twiddles)[i - 1], w, current_config.modulus);
    }
    
    (*twiddles_sr)[0] = 1;
    for (unsigned i = 1; i < n/4; i++) {
        (*twiddles_sr)[i] = mod_mul((*twiddles_sr)[i - 1], w3, current_config.modulus);
    }
}

static void burrus84_ntt_fixed(uint64_t * restrict out, const uint64_t * restrict in, unsigned n, unsigned m)
{
    uint64_t *twiddles = NULL, *twiddles_sr = NULL;
    generate_twiddles(&twiddles, &twiddles_sr, n);
    
    NTT_BIT_REVERSE_LOOP(i, j, n, {
        out[i] = in[j];
    });
    
    for (unsigned is = 0, id = 4; is < n; ) {
        for (unsigned i0 = is; i0 < n; i0 += id) {
            ntt_ct_dit_bf2_0_fixed(1, 1, out + i0, out + i0);
        }
        is = 2 * id - 2;
        id = 4 * id;
    }
    
    if (m >= 2) {
        for (unsigned k = 2; k <= m; k++) {
            unsigned n2 = 1 << k;
            unsigned n4 = n2 >> 2;
            
            for (unsigned j = 0; j < n4; j++) {
                unsigned t = j << (m - k);
                uint64_t w1 = twiddles[t];
                uint64_t w3 = twiddles_sr[t];
                
                for (unsigned is = j, id = 2 << k; is < n; ) {
                    for (unsigned i0 = is; i0 < n; i0 += id) {
                        ntt_sr_dit_bf4_fixed(n4, out + i0, w1, w3);
                    }
                    is = 2 * id - n2 + j;
                    id = 4 * id;
                }
            }
        }
    }
    
    free(twiddles);
    free(twiddles_sr);
}

static void burrus84_intt_fixed(uint64_t * restrict out, const uint64_t * restrict in, unsigned n, unsigned m)
{
    uint64_t *inv_twiddles = NULL, *inv_twiddles_sr = NULL;
    
    inv_twiddles = malloc(n * sizeof(uint64_t));
    inv_twiddles_sr = malloc((n/4) * sizeof(uint64_t));
    assert(inv_twiddles && inv_twiddles_sr);
    
    uint64_t w_inv = mod_pow(current_config.root, current_config.modulus - 1 - (current_config.modulus - 1) / n, current_config.modulus);
    uint64_t w3_inv = mod_mul(mod_mul(w_inv, w_inv, current_config.modulus), w_inv, current_config.modulus);
    
    inv_twiddles[0] = 1;
    for (unsigned i = 1; i < n; i++) {
        inv_twiddles[i] = mod_mul(inv_twiddles[i - 1], w_inv, current_config.modulus);
    }
    
    inv_twiddles_sr[0] = 1;
    for (unsigned i = 1; i < n/4; i++) {
        inv_twiddles_sr[i] = mod_mul(inv_twiddles_sr[i - 1], w3_inv, current_config.modulus);
    }
    
    NTT_BIT_REVERSE_LOOP(i, j, n, {
        out[i] = in[j];
    });
    
    for (unsigned is = 0, id = 4; is < n; ) {
        for (unsigned i0 = is; i0 < n; i0 += id) {
            ntt_ct_dit_bf2_0_fixed(1, 1, out + i0, out + i0);
        }
        is = 2 * id - 2;
        id = 4 * id;
    }
    
    if (m >= 2) {
        for (unsigned k = 2; k <= m; k++) {
            unsigned n2 = 1 << k;
            unsigned n4 = n2 >> 2;
            
            for (unsigned j = 0; j < n4; j++) {
                unsigned t = j << (m - k);
                uint64_t w1_inv = inv_twiddles[t];
                uint64_t w3_inv = inv_twiddles_sr[t];
                
                for (unsigned is = j, id = 2 << k; is < n; ) {
                    for (unsigned i0 = is; i0 < n; i0 += id) {
                        ntt_sr_dit_bf4_fixed(n4, out + i0, w1_inv, w3_inv);
                    }
                    is = 2 * id - n2 + j;
                    id = 4 * id;
                }
            }
        }
    }
    
    uint64_t n_inv = mod_inverse(n, current_config.modulus);
    for (unsigned i = 0; i < n; i++) {
        out[i] = mod_mul(out[i], n_inv, current_config.modulus);
    }
    
    free(inv_twiddles);
    free(inv_twiddles_sr);
}

void ntt_init(const NttConfig *config, unsigned n)
{
    assert(config != NULL);
    assert((n & (n - 1)) == 0);
    
    current_config = *config;
    ntt_modulus_global = config->modulus;
}

void ntt_cleanup(void)
{
    ntt_modulus_global = 0;
}

void ntt_forward(unsigned n, uint64_t * restrict out, const uint64_t * restrict in)
{
    assert(out != NULL && in != NULL);
    assert((n & (n - 1)) == 0);
    
    unsigned log2_n = 31 - clz32(n);
    burrus84_ntt_fixed(out, in, n, log2_n);
}

void ntt_inverse(unsigned n, uint64_t * restrict out, const uint64_t * restrict in)
{
    assert(out != NULL && in != NULL);
    assert((n & (n - 1)) == 0);
    
    unsigned log2_n = 31 - clz32(n);
    burrus84_intt_fixed(out, in, n, log2_n);
} 