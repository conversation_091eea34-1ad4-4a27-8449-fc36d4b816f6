# Split-Radix NTT 串行实现

## 概述

这是一个基于 Split-Radix 算法的数论变换（Number Theoretic Transform, NTT）串行实现。NTT 是 FFT 在有限域上的应用，特别适用于模运算环境下的快速卷积计算。

## 算法特点

- **算法**: Burrus84 Split-Radix DIT (Decimation In Time)
- **复杂度**: O(n log n)
- **优势**: 相比标准 Radix-2 算法减少约 20% 的运算量
- **适用**: 2 的幂次长度的序列

## 文件结构

```
ntt.h                  - NTT 主接口定义
ntt_modular.c          - 模运算基础函数
ntt_twiddles.h/.c      - 旋转因子管理
ntt_bf.h               - 蝶形运算定义
ntt_bit_reverse.h      - 位反转操作
ntt_split_radix.c      - Split-Radix NTT 主实现
ntt_test.c             - 测试程序
Makefile.ntt           - 编译配置
```

## 核心算法

### Split-Radix 分解

Split-Radix 算法将长度为 N 的 NTT 分解为：
- 1 个长度为 N/2 的 NTT（偶数索引）
- 2 个长度为 N/4 的 NTT（奇数索引的实部和虚部）

### 蝶形运算

4点 Split-Radix 蝶形运算：
```
X[k] = A[k] + W₁C[k] + W₃D[k]
X[k+N/2] = A[k] - W₁C[k] - W₃D[k]  
X[k+N/4] = B[k] + j(W₁C[k] - W₃D[k])
X[k+3N/4] = B[k] - j(W₁C[k] - W₃D[k])
```

其中 W₁ = e^(-2πjk/N), W₃ = e^(-6πjk/N)

## 使用方法

### 1. 初始化

```c
#include "ntt.h"

// 配置 NTT 参数（使用素数 998244353）
NttConfig config = {
    .modulus = 998244353,    // 模数（素数）
    .root = 3,               // 本原根
    .max_log2_n = 20         // 最大支持 2^20 = 1M 点
};

// 初始化 NTT 系统
unsigned n = 1024;  // 变换长度
ntt_init(&config, n);
```

### 2. 正向变换

```c
uint64_t input[1024] = {...};
uint64_t output[1024];

ntt_forward(n, output, input);
```

### 3. 逆向变换

```c
uint64_t forward_result[1024];
uint64_t recovered[1024];

ntt_inverse(n, recovered, forward_result);
```

### 4. 清理资源

```c
ntt_cleanup();
```

## 卷积计算示例

```c
// 计算两个多项式的卷积
uint64_t a[8] = {1, 2, 3, 4, 0, 0, 0, 0};
uint64_t b[8] = {1, 1, 1, 1, 0, 0, 0, 0};
uint64_t a_ntt[8], b_ntt[8], c_ntt[8], result[8];

// 正向 NTT
ntt_forward(8, a_ntt, a);
ntt_forward(8, b_ntt, b);

// 点乘
for (int i = 0; i < 8; i++) {
    c_ntt[i] = mod_mul(a_ntt[i], b_ntt[i], config.modulus);
}

// 逆向 NTT 得到卷积结果
ntt_inverse(8, result, c_ntt);
```

## 编译和测试

```bash
# 编译
make -f Makefile.ntt

# 运行测试
make -f Makefile.ntt test

# 编译调试版本
make -f Makefile.ntt debug

# 清理
make -f Makefile.ntt clean
```

## 性能特点

- **时间复杂度**: O(n log n)
- **空间复杂度**: O(n) 用于旋转因子存储
- **运算量**: 相比 Radix-2 算法减少约 20% 的乘法运算
- **适用规模**: 支持 2^1 到 2^20 长度的变换

## 数学原理

### 模数选择

使用的模数 998244353 = 119 × 2^23 + 1，满足：
- 是素数
- 形式为 k × 2^m + 1，支持高阶 2 的幂次变换
- 3 是其本原根

### 旋转因子

- W₁ = g^((p-1)/n) mod p，其中 g 是本原根
- W₃ = (W₁)³ mod p
- 逆变换使用 W₁⁻¹ 和 W₃⁻¹

## 应用场景

1. **大整数乘法**: 快速计算超大整数的乘积
2. **多项式运算**: 多项式乘法、除法等运算
3. **数字信号处理**: 在有限域上的信号处理
4. **密码学**: 基于格的密码算法中的多项式运算

## 注意事项

1. 输入长度必须是 2 的幂次
2. 所有运算都在指定模数下进行
3. 需要确保模数和本原根的正确性
4. 逆变换需要乘以 n 的模逆元 