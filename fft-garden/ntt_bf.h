/**
 * @file ntt_bf.h
 * @brief Split-Radix NTT 蝶形运算定义
 * @details 实现 Split-Radix NTT 的核心蝶形运算函数
 */

#ifndef _NTT_BF_H_
#define _NTT_BF_H_

#include "ntt.h"
#include "ntt_twiddles.h"

/**
 * @brief Split-Radix NTT 4点蝶形运算
 * @param s 步长
 * @param out 输出数组指针
 * @param w1 第一个旋转因子
 * @param w3 第二个旋转因子(w^3)
 */
static inline void ntt_sr_dit_bf4(unsigned s, uint64_t * restrict out, uint64_t w1, uint64_t w3)
{
    uint64_t a = out[0];
    uint64_t b = out[s];
    uint64_t c = out[s * 2];
    uint64_t d = out[s * 3];
    
    uint64_t w1c = mod_mul(w1, c, ntt_modulus);
    uint64_t w3d = mod_mul(w3, d, ntt_modulus);
    
    uint64_t sum_wd = (w1c + w3d) % ntt_modulus;
    uint64_t diff_wd = (w1c + ntt_modulus - w3d) % ntt_modulus;
    
    out[0] = (a + sum_wd) % ntt_modulus;
    out[s * 2] = (a + ntt_modulus - sum_wd) % ntt_modulus;
    out[s] = (b + diff_wd) % ntt_modulus;
    out[s * 3] = (b + ntt_modulus - diff_wd) % ntt_modulus;
}

/**
 * @brief Split-Radix NTT 4点蝶形运算(旋转因子为1时的优化版本)
 * @param s 步长
 * @param out 输出数组指针
 */
static inline void ntt_sr_dit_bf4_0(unsigned s, uint64_t * restrict out)
{
    uint64_t a = out[0];
    uint64_t b = out[s];
    uint64_t c = out[s * 2];
    uint64_t d = out[s * 3];
    
    uint64_t sum_cd = (c + d) % ntt_modulus;
    uint64_t diff_cd = (c + ntt_modulus - d) % ntt_modulus;
    
    out[0] = (a + sum_cd) % ntt_modulus;
    out[s * 2] = (a + ntt_modulus - sum_cd) % ntt_modulus;
    out[s] = (b + diff_cd) % ntt_modulus;
    out[s * 3] = (b + ntt_modulus - diff_cd) % ntt_modulus;
}

/**
 * @brief Cooley-Tukey 2点蝶形运算(旋转因子为1时)
 * @param s1 第一个步长
 * @param s2 第二个步长
 * @param out1 第一个输出指针
 * @param in2 第二个输入指针
 */
static inline void ntt_ct_dit_bf2_0(unsigned s1, unsigned s2, uint64_t * restrict out1, const uint64_t * restrict in2)
{
    uint64_t a = *in2;
    uint64_t b = *(in2 + s2);
    
    *out1 = (a + b) % ntt_modulus;
    *(out1 + s1) = (a + ntt_modulus - b) % ntt_modulus;
}

#endif 