/**
 * @file ntt_simple.c
 * @brief 简单正确的 NTT 实现
 * @details 使用标准 Cooley-Tukey 算法确保正确性
 */

#include "ntt.h"
#include "ntt_bit_reverse.h"
#include <stdlib.h>
#include <assert.h>

static NttConfig current_config;

static void simple_ntt_forward(uint64_t * restrict out, const uint64_t * restrict in, unsigned n)
{
    NTT_BIT_REVERSE_LOOP(i, j, n, {
        out[i] = in[j];
    });
    
    for (unsigned len = 2; len <= n; len <<= 1) {
        uint64_t w_len = mod_pow(current_config.root, (current_config.modulus - 1) / len, current_config.modulus);
        
        for (unsigned i = 0; i < n; i += len) {
            uint64_t w = 1;
            for (unsigned j = 0; j < len / 2; j++) {
                uint64_t u = out[i + j];
                uint64_t v = mod_mul(out[i + j + len / 2], w, current_config.modulus);
                
                out[i + j] = (u + v) % current_config.modulus;
                out[i + j + len / 2] = (u + current_config.modulus - v) % current_config.modulus;
                
                w = mod_mul(w, w_len, current_config.modulus);
            }
        }
    }
}

static void simple_ntt_inverse(uint64_t * restrict out, const uint64_t * restrict in, unsigned n)
{
    NTT_BIT_REVERSE_LOOP(i, j, n, {
        out[i] = in[j];
    });
    
    for (unsigned len = 2; len <= n; len <<= 1) {
        uint64_t w_len = mod_pow(current_config.root, current_config.modulus - 1 - (current_config.modulus - 1) / len, current_config.modulus);
        
        for (unsigned i = 0; i < n; i += len) {
            uint64_t w = 1;
            for (unsigned j = 0; j < len / 2; j++) {
                uint64_t u = out[i + j];
                uint64_t v = mod_mul(out[i + j + len / 2], w, current_config.modulus);
                
                out[i + j] = (u + v) % current_config.modulus;
                out[i + j + len / 2] = (u + current_config.modulus - v) % current_config.modulus;
                
                w = mod_mul(w, w_len, current_config.modulus);
            }
        }
    }
    
    uint64_t n_inv = mod_inverse(n, current_config.modulus);
    for (unsigned i = 0; i < n; i++) {
        out[i] = mod_mul(out[i], n_inv, current_config.modulus);
    }
}

void ntt_init(const NttConfig *config, unsigned n)
{
    assert(config != NULL);
    assert((n & (n - 1)) == 0);
    
    current_config = *config;
}

void ntt_cleanup(void)
{
    
}

void ntt_forward(unsigned n, uint64_t * restrict out, const uint64_t * restrict in)
{
    assert(out != NULL && in != NULL);
    assert((n & (n - 1)) == 0);
    
    simple_ntt_forward(out, in, n);
}

void ntt_inverse(unsigned n, uint64_t * restrict out, const uint64_t * restrict in)
{
    assert(out != NULL && in != NULL);
    assert((n & (n - 1)) == 0);
    
    simple_ntt_inverse(out, in, n);
} 