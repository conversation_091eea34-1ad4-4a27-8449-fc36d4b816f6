#!/usr/bin/env python3
"""
全面的NTT友好素数搜索脚本
寻找更多可用的模数，特别适用于大规模多项式乘法
"""

import time

def is_prime(n):
    """素性检测"""
    if n < 2: return False
    if n == 2: return True
    if n % 2 == 0: return False
    for i in range(3, int(n**0.5) + 1, 2):
        if n % i == 0: return False
    return True

def mod_pow(a, b, m):
    """快速模幂"""
    res = 1
    a %= m
    while b > 0:
        if b & 1: res = (res * a) % m
        a = (a * a) % m
        b >>= 1
    return res

def check_primitive_root_3(p):
    """检查3是否是模p的原根"""
    if not is_prime(p): return False
    if mod_pow(3, p - 1, p) != 1: return False
    
    phi = p - 1
    factors = []
    n = phi
    for d in range(2, int(n**0.5) + 1):
        if n % d == 0:
            factors.append(d)
            while n % d == 0: n //= d
    if n > 1: factors.append(n)
    
    for q in factors:
        if mod_pow(3, phi // q, p) == 1:
            return False
    return True

def search_ntt_primes_range(n_min, n_max, k_max=1000):
    """在指定范围内搜索NTT友好素数"""
    results = []
    
    for n in range(n_min, n_max + 1):
        power_2_n = 1 << n
        print(f"搜索 n={n} (2^{n} = {power_2_n:,}): ", end="", flush=True)
        
        found_count = 0
        candidates_for_n = []
        
        for k in range(1, k_max + 1, 2):  # k必须是奇数
            p = k * power_2_n + 1
            
            if p > (1 << 31) - 1:  # 32位范围
                break
                
            if is_prime(p) and check_primitive_root_3(p):
                candidates_for_n.append((p, k, n))
                found_count += 1
                
                if found_count <= 5:  # 显示前5个
                    print(f"{p}({k}) ", end="")
        
        results.extend(candidates_for_n)
        print(f"-> 总共{found_count}个")
    
    return results

def analyze_combinations(primes_list):
    """分析不同的三模组合"""
    print("\n分析不同精度范围的三模组合:")
    print("=" * 60)
    
    # 按n值分组
    by_n = {}
    for p, k, n in primes_list:
        if n not in by_n:
            by_n[n] = []
        by_n[n].append((p, k, n))
    
    # 选择不同策略的组合
    combinations = []
    
    # 策略1: 最高精度 (选择最大的n值)
    max_n_values = sorted(by_n.keys(), reverse=True)[:3]
    if len(max_n_values) >= 3:
        combo1 = []
        for n in max_n_values:
            # 选择该n值下k最小的素数
            best = min(by_n[n], key=lambda x: x[1])
            combo1.append(best)
        combinations.append(("最高精度", combo1))
    
    # 策略2: 平衡精度和性能
    if len(max_n_values) >= 3:
        mid_n_values = sorted(by_n.keys())[len(by_n)//3:len(by_n)*2//3]
        if len(mid_n_values) >= 3:
            combo2 = []
            for n in mid_n_values[:3]:
                best = min(by_n[n], key=lambda x: x[1])
                combo2.append(best)
            combinations.append(("平衡性能", combo2))
    
    # 策略3: 兼容现有系统
    current_primes = {998244353, 1004535809, 469762049}
    available_new = [x for x in primes_list if x[0] not in current_primes]
    if available_new:
        combo3 = [(998244353, 119, 23), (1004535809, 479, 21)]
        combo3.append(max(available_new, key=lambda x: x[2]))  # 选择n最大的新素数
        combinations.append(("兼容现有", combo3))
    
    for strategy, combo in combinations:
        print(f"\n{strategy}策略:")
        total_product = 1
        for p, k, n in combo:
            max_ntt = 1 << n
            print(f"  {p:>10}u,  // {k:>3} * 2^{n:>2} + 1  (最大NTT: 2^{n:>2} = {max_ntt:>8,})")
            total_product *= p
        
        print(f"  三模乘积: {total_product:.2e}")
        
        # 计算相对于当前系统的精度提升
        current_product = 998244353 * 1004535809 * 469762049
        improvement = total_product / current_product
        print(f"  精度提升: {improvement:.2f}倍")

def generate_c_code(primes_list):
    """生成C++代码"""
    print("\nC++代码模板:")
    print("=" * 40)
    
    # 选择前10个最好的素数
    best_primes = sorted(primes_list, key=lambda x: (-x[2], x[1]))[:10]
    
    print("// 可选的NTT友好素数")
    print("constexpr uint32_t ntt_primes[] = {")
    for p, k, n in best_primes:
        max_ntt = 1 << n
        print(f"    {p:>10}u,  // {k:>3} * 2^{n:>2} + 1, max_ntt_size = {max_ntt:>8,}")
    print("};")
    
    print("\n// 推荐的三模组合")
    print("constexpr uint32_t recommended_combo[] = {")
    for i, (p, k, n) in enumerate(best_primes[:3]):
        print(f"    {p}u,  // 模数{i+1}: {k} * 2^{n} + 1")
    print("};")

def main():
    print("NTT友好素数全面搜索")
    print("=" * 50)
    
    start_time = time.time()
    
    # 搜索不同范围的素数
    print("搜索范围: n=17 到 n=28 (支持131K到256M的变换)")
    print("-" * 50)
    
    all_primes = search_ntt_primes_range(17, 28, k_max=500)
    
    print(f"\n搜索完成，总共找到 {len(all_primes)} 个NTT友好素数")
    print(f"搜索耗时: {time.time() - start_time:.2f} 秒")
    
    if all_primes:
        # 分析组合
        analyze_combinations(all_primes)
        
        # 生成代码
        generate_c_code(all_primes)
        
        # 特殊推荐
        print(f"\n特殊推荐:")
        print("-" * 20)
        
        # 找到支持最大变换的素数
        max_n_prime = max(all_primes, key=lambda x: x[2])
        p, k, n = max_n_prime
        max_transform = 1 << n
        print(f"最大变换支持: {p} = {k} * 2^{n} + 1  (2^{n} = {max_transform:,})")
        
        # 找到k值最小的素数们
        min_k_primes = sorted(all_primes, key=lambda x: x[1])[:5]
        print(f"\n最简形式的素数 (k值最小):")
        for p, k, n in min_k_primes:
            max_transform = 1 << n
            print(f"  {p} = {k} * 2^{n} + 1  (2^{n} = {max_transform:,})")

if __name__ == "__main__":
    main() 