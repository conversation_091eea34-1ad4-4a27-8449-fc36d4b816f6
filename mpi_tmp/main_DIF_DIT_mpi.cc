#include <cstring>
#include <string>
#include <iostream>
#include <fstream>
#include <chrono>
#include <iomanip>
#include <sys/time.h>
#include <algorithm>
#include <mpi.h>
#include <vector>

typedef long long ll;

// fRead is no longer distributed, only rank 0 reads.
// It's mostly integrated into main's rank 0 block.

void fWrite(int *ab, int n, int input_id) {
    std::string str1 = "files/";
    std::string str2 = std::to_string(input_id);
    std::string strout = str1 + str2 + ".out";
    std::ofstream fout(strout);
    // Output 2*n-1 elements for convolution result
    int output_count = (n == 0) ? 0 : (2 * n - 1);
    for (int i = 0; i < output_count; i++) {
        fout << ab[i] << '\n';
    }
    fout.close(); // Good practice to close the file stream
}

// Add fCheck function similar to the one in main_barrett_radix4_ntt.cc
void fCheck(int *ab, int n, int input_id, int rank) {
    if (rank == 0) { // Only rank 0 performs the check
        if (n <= 0) { // Cannot check if n is not positive
            // std::cout << "ID " << input_id << ": Skipping fCheck due to n <= 0." << std::endl;
            return;
        }
        std::string path = "nttdata/" + std::to_string(input_id) + ".out";
        std::ifstream fin(path);
        if (!fin) {
            std::cerr << "Rank 0, ID " << input_id << ": fCheck - 无法打开参考文件 " << path << std::endl;
            return;
        }

        bool match = true;
        int result_len = 2 * n - 1;
        for (int i = 0; i < result_len; ++i) {
            int expected_val;
            if (!(fin >> expected_val)) {
                std::cerr << "Rank 0, ID " << input_id << ": fCheck - 读取参考文件 " << path << " 时发生错误或文件长度不足 (i=" << i << ")." << std::endl;
                match = false;
                break;
            }
            if (ab[i] != expected_val) {
                // std::cout << "Rank 0, ID " << input_id << ": fCheck - 结果错误! Index " << i << ", Expected: " << expected_val << ", Got: " << ab[i] << std::endl;
                match = false; // Continue checking to see all errors, or break if only first error is needed
                               // For now, let's just mark as mismatch and continue loop to see if file read fails.
            }
        }
        fin.close();

        if (match) {
            // Check if we read fewer than expected values from file (e.g. if file was too short but values matched)
            // This check is implicitly covered if `fin >> expected_val` failed and set match to false.
            // A more explicit check could be to count how many values were successfully read.
            std::cout << "ID " << input_id << ": 多项式乘法结果正确 (fCheck)" << std::endl;
        } else {
            std::cout << "ID " << input_id << ": 多项式乘法结果错误 (fCheck)" << std::endl;
        }
    }
}

inline ll fpow(ll a, ll b, int P) {
    ll res = 1; a %= P;
    if (a < 0) a += P; // Ensure a is non-negative before modulo operations
    for (; b; b >>= 1) {
        if (b & 1) res = (res * a) % P;
        a = (a * a) % P;
    }
    return res;
}

// calc_powg, DIF, DIT functions remain largely the same as they are called by all processes
// on their local data. Ensure they handle P correctly if a can be negative from (g-h).
void calc_powg(int w[], int G, int P, int gen) {
    w[0] = 1; ll f;
    const int g_root = fpow(gen, (P - 1) / G, P); // Renamed 'g' to 'g_root' to avoid conflict
    for (int t = 0; (1 << (t + 1)) < G; ++t) {
        f = w[1 << t] = fpow(g_root, G >> (t + 2), P);
        for (int x = 1 << t; x < 1 << (t + 1); ++x)
            w[x] = (ll)f * w[x - (1 << t)] % P;
    }
}

void DIF(int f[], int l, int P, int w[]) {
    int lim = 1 << l;
    ll g, h;
    for (int len = lim; len > 1; len >>= 1) {
        for (int st = 0, t = 0; st < lim; st += len, ++t) {
            for (int i = st; i < st + len / 2; ++i) {
                g = f[i];
                h = (ll)f[i + len / 2] * w[t] % P;
                f[i] = (g + h) % P;
                f[i + len / 2] = (P + g - h) % P; // Ensure positive before modulo
            }
        }
    }
}

void DIT(int f[], int l, int P, int w[]) {
    int lim = 1 << l;
    ll g, h;
    for (int len = 2; len <= lim; len <<= 1) {
        for (int st = 0, t = 0; st < lim; st += len, ++t) {
            for (int i = st; i < st + len / 2; ++i) {
                g = f[i];
                h = f[i + len / 2];
                f[i] = (g + h) % P;
                f[i + len / 2] = (P + g - h) % P * w[t] % P; // Corrected: ensure (P+g-h) is positive, then apply w[t]
            }
        }
    }
    const ll invl = fpow(lim, P - 2, P);
    for (int i = 0; i < lim; ++i)
        f[i] = (invl * f[i]) % P;
    std::reverse(f + 1, f + lim);
}

void poly_multiply_optimized(int *a_input_from_main, int *b_input_from_main, int *ab_output_from_main,
                             int n, int p, int gen, int rank, int size) {
    if (n == 0) { // Handle n=0 case to prevent issues with 2*n-1 or log calculations
        if (rank == 0 && ab_output_from_main != nullptr) {
            // Output for n=0 is typically empty or defined by problem spec (e.g. 0 elements)
        }
        return;
    }

    int l = 0;
    // Ensure 2*n doesn't overflow if n is very large, though n is int here.
    // (1<<l) should be at least 2*n-1 for convolution, or 2*n for some NTT defs.
    // The loop `while ((1 << l) < 2 * n)` is typical for finding next power of 2 for 2n elements.
    while ((1 << l) < (n == 1 ? 2 : (2 * n))) { // Ensure lim is at least 2 for n=1, and 2*n otherwise
        l++;
        if (l > 25 && rank == 0) { // Safety break for too large l
             std::cerr << "Warning: l is very large: " << l << " for n=" << n << std::endl;
             MPI_Abort(MPI_COMM_WORLD, 1);
        }
    }
    int lim = 1 << l;

    std::vector<int> A_vec(lim, 0); 
    std::vector<int> B_vec(lim, 0); 
    std::vector<int> w_vec(lim, 0); 

    if (rank == 0) {
        if (n > 0) { // only copy if n > 0
            memcpy(A_vec.data(), a_input_from_main, n * sizeof(int));
            memcpy(B_vec.data(), b_input_from_main, n * sizeof(int));
        }
    }
    
    // Broadcast only the first n elements that were potentially filled by rank 0
    MPI_Bcast(A_vec.data(), n, MPI_INT, 0, MPI_COMM_WORLD); 
    MPI_Bcast(B_vec.data(), n, MPI_INT, 0, MPI_COMM_WORLD); 
    // After Bcast, all processes have A_vec and B_vec zero-padded correctly up to lim,
    // with the first n elements from original input.

    calc_powg(w_vec.data(), lim, p, gen);

    DIF(A_vec.data(), l, p, w_vec.data());
    DIF(B_vec.data(), l, p, w_vec.data());

    for (int i = 0; i < lim; ++i) {
        A_vec[i] = (ll)A_vec[i] * B_vec[i] % p;
    }

    DIT(A_vec.data(), l, p, w_vec.data());

    if (rank == 0) {
        int result_len = 2 * n - 1;
        if (result_len < 0) result_len = 0; // if n=0, result_len would be -1
        
        if (ab_output_from_main != nullptr && result_len > 0) { // Check for nullptr
             memcpy(ab_output_from_main, A_vec.data(), std::min(result_len, lim) * sizeof(int));
        }
    }
}

int main(int argc, char *argv[]) {
    MPI_Init(&argc, &argv);

    int rank, size;
    MPI_Comm_rank(MPI_COMM_WORLD, &rank);
    MPI_Comm_size(MPI_COMM_WORLD, &size);

    // Define a reasonable max size for n based on typical inputs or problem constraints
    // This helps in pre-allocating vectors to a safe size.
    const int MAX_N_EXPECTED = 1 << 18; // Example: up to 2^18 
                                     // (adjust based on /nttdata constraints, e.g. 131072 implies 2^17)
    std::vector<int> main_a(MAX_N_EXPECTED);
    std::vector<int> main_b(MAX_N_EXPECTED);
    // Convolution result can be up to 2*N-1. If N is MAX_N_EXPECTED, then 2*MAX_N_EXPECTED.
    std::vector<int> main_ab(2 * MAX_N_EXPECTED); 

    int test_begin = 0;
    int test_end = 3; // User had 0 to 3
    for (int i = test_begin; i <= test_end; ++i) {
        int n_val = 0, p_val = 0; // Initialize
        
        if (rank == 0) {
            std::string str1 = "/nttdata/"; // Changed from "..//nttdata/"
            std::string str2 = std::to_string(i);
            std::string strin = str1 + str2 + ".in";
            std::ifstream fin(strin);
            if (!fin) {
                std::cerr << "Rank 0: 无法打开文件 " << strin << std::endl;
                // MPI_Abort is a collective operation, but rank 0 exiting might be enough
                // Or use a flag broadcasted to other ranks to exit gracefully.
                MPI_Abort(MPI_COMM_WORLD, 1); 
            }
            fin >> n_val >> p_val;
            if (n_val > MAX_N_EXPECTED) {
                 std::cerr << "Rank 0: Input n_val " << n_val 
                           << " for id " << i << " exceeds MAX_N_EXPECTED " << MAX_N_EXPECTED << std::endl;
                 MPI_Abort(MPI_COMM_WORLD, 1);
            }
            if (n_val < 0) { // Basic sanity check
                 std::cerr << "Rank 0: Input n_val " << n_val << " is negative for id " << i << std::endl;
                 MPI_Abort(MPI_COMM_WORLD, 1);
            }
            for (int k = 0; k < n_val; ++k) {
                if (!(fin >> main_a[k])) { 
                    std::cerr << "Rank 0: Error reading a[" << k << "] for id " << i << std::endl; 
                    MPI_Abort(MPI_COMM_WORLD,1);
                }
            }
            for (int k = 0; k < n_val; ++k) {
                 if (!(fin >> main_b[k])) {
                    std::cerr << "Rank 0: Error reading b[" << k << "] for id " << i << std::endl; 
                    MPI_Abort(MPI_COMM_WORLD,1);
                 }
            }
            fin.close();
        }

        MPI_Bcast(&n_val, 1, MPI_INT, 0, MPI_COMM_WORLD);
        MPI_Bcast(&p_val, 1, MPI_INT, 0, MPI_COMM_WORLD);

        // All processes now have n_val and p_val.
        // If n_val is 0 or invalid, other ranks might proceed if not checked.
        if (n_val <= 0 && rank == 0) { // Only rank 0 prints, others will skip poly_multiply
            std::cout << "n = " << n_val << " p = " << p_val 
                      << " for id " << i << ". Skipping computation due to n <= 0." << std::endl;
        }
        if (n_val <= 0) { // All ranks should skip if n is not positive
            if (rank == 0) { /* rank 0 might still want to write an empty/specific output file */ 
                fWrite(main_ab.data(), n_val, i); // Writes 0 elements if n_val <=0 due to fWrite logic
            }
            continue; // Skip to next test case
        }
        
        std::fill(main_ab.begin(), main_ab.begin() + (2*n_val > 0 ? 2*n_val-1 : 0), 0);


        auto Start = std::chrono::high_resolution_clock::now();
        poly_multiply_optimized(main_a.data(), main_b.data(), main_ab.data(), n_val, p_val, 3, rank, size);
        auto End = std::chrono::high_resolution_clock::now();
        
        if (rank == 0) {
            std::chrono::duration<double> elapsed_s = End - Start;
            fWrite(main_ab.data(), n_val, i);
            fCheck(main_ab.data(), n_val, i, rank); // Call fCheck
            std::cout << "id=" << i << " n=" << n_val << " p=" << p_val << " procs=" << size 
                      << " time=" << elapsed_s.count() * 1e6 << " us" << std::endl; // Output in us using seconds directly
        }
    }

    MPI_Finalize();
    return 0;
}
