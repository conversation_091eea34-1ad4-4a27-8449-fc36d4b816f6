#include <iostream>
#include <vector>
#include <algorithm>
using namespace std;

int mod_pow(long long x, long long y, int p) {
    long long r = 1 % p;
    x %= p;
    while (y) {
        if (y & 1) r = r * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return int(r);
}

void bitrev(vector<int>& a) {
    int n = a.size();
    int lg = __builtin_ctz(n);
    for (int i = 0; i < n; ++i) {
        int rev = 0;
        for (int j = 0; j < lg; ++j) {
            if (i >> j & 1) rev |= 1 << (lg - 1 - j);
        }
        if (i < rev) swap(a[i], a[rev]);
    }
}

void simple_ntt(vector<int>& a, bool inv, int p) {
    int n = a.size();
    bitrev(a);
    
    for (int len = 2; len <= n; len <<= 1) {
        int wn = mod_pow(3, (p - 1) / len, p);
        if (inv) wn = mod_pow(wn, p - 2, p);
        
        for (int i = 0; i < n; i += len) {
            int w = 1;
            for (int j = 0; j < len / 2; ++j) {
                int u = a[i + j];
                int v = 1LL * a[i + j + len / 2] * w % p;
                a[i + j] = (u + v) % p;
                a[i + j + len / 2] = (u - v + p) % p;
                w = 1LL * w * wn % p;
            }
        }
    }
    
    if (inv) {
        int invN = mod_pow(n, p - 2, p);
        for (int& x : a) {
            x = 1LL * x * invN % p;
        }
    }
}

vector<int> poly_mul(vector<int> a, vector<int> b, int p) {
    int n = a.size();
    int N = 1;
    while (N < 2 * n) N <<= 1;
    
    a.resize(N);
    b.resize(N);
    
    simple_ntt(a, false, p);
    simple_ntt(b, false, p);
    
    for (int i = 0; i < N; i++) {
        a[i] = 1LL * a[i] * b[i] % p;
    }
    
    simple_ntt(a, true, p);
    
    a.resize(2 * n - 1);
    return a;
}

int main() {
    vector<int> a = {4, 1, 5, 2};
    vector<int> b = {1, 5, 5, 4};
    int p = 7340033;
    
    auto result = poly_mul(a, b, p);
    
    cout << "Simple NTT result: ";
    for (int x : result) {
        cout << x << " ";
    }
    cout << endl;
    
    return 0;
}
