/****************************************************************************************
 * mpi_case.cpp   ——  Case-level Barrett NTT 并行 (Radix-2, 任意 2^k 点)
 *
 *  1) 与 serial 版本相比，只在 main 中加入 MPI 逻辑，算法本身完全复用
 *  2) 分配策略：编号 first..last 的测试用例，满足
 *            (id - first) % size == rank     → 归 rank 进程计算
 *  3) 编译：mpic++ -O3 -std=c++20 -march=native mpi_case.cpp -o ntt_case
 *     运行：mpirun -np 4 ./ntt_case           # 进程数按需调整
 ****************************************************************************************/
#include <bits/stdc++.h>
#include <mpi.h>
using namespace std;

using u32  = uint32_t;
using u64  = uint64_t;
#if defined(_MSC_VER) && !defined(__clang__)
using u128 = unsigned __int128;
#else
using u128 = __uint128_t;
#endif

/* ---------------- 固定 I/O ---------------- */
void fRead(int *a, int *b, int *n, int *p, int input_id) {
    string path = "../nttdata/" + to_string(input_id) + ".in";
    ifstream fin(path);
    if(!fin) { cerr << "无法打开输入文件: " << path << '\n'; MPI_Abort(MPI_COMM_WORLD, 1); }
    fin >> *n >> *p;
    for (int i = 0; i < *n; ++i) fin >> a[i];
    for (int i = 0; i < *n; ++i) fin >> b[i];
}
void fCheck(int *ab, int n, int input_id) {
    string path = "../nttdata/" + to_string(input_id) + ".out";
    ifstream fin(path);
    if(!fin) { cerr << "无法打开输出文件: " << path << '\n'; MPI_Abort(MPI_COMM_WORLD, 1); }
    for (int i = 0; i < 2 * n - 1; ++i) {
        int x; fin >> x;
        if (x != ab[i]) { cout << "多项式乘法结果错误 (id="<<input_id<<")\n"; return; }
    }
    cout << "多项式乘法结果正确 (id="<<input_id<<")\n";
}
void fWrite(int *ab, int n, int input_id) {
    string path = "files/" + to_string(input_id) + ".out";
    ofstream fout(path);
    for (int i = 0; i < 2 * n - 1; ++i) fout << ab[i] << '\n';
}

/* -------------- Barrett 快速取模 -------------- */
class Barrett {
public:
    explicit Barrett(u32 m): mod(m) {
        inv = (static_cast<u128>(1) << 64) / m;
    }
    inline u32 reduce(u64 x) const {
        u64 q = (static_cast<u128>(x) * inv) >> 64;
        u64 r = x - q * mod;
        if (r >= mod) r -= mod;
        return static_cast<u32>(r);
    }
    inline u32 mul(u32 a,u32 b) const {
        return reduce(static_cast<u64>(a)*b);
    }
    const u32 mod;
private:
    u64 inv;
};

/* -------------- 工具函数 ---------------------- */
static u32 mod_pow(u32 a, u64 e, u32 mod) {
    u64 res = 1, base = a;
    while (e) {
        if (e & 1) res = res * base % mod;
        base = base * base % mod;
        e >>= 1;
    }
    return static_cast<u32>(res);
}
static void bit_reverse(vector<int>& rev,int n) {
    int lg = __builtin_ctz(n);
    rev.resize(n);
    for (int i=0;i<n;++i) rev[i]=(rev[i>>1]>>1)|((i&1)<<(lg-1));
}

/* -------------- Radix-2 串行 NTT -------------- */
void ntt(vector<u32>& a,bool inverse,const Barrett& br,u32 g=3) {
    int n=a.size();
    vector<int> rev; bit_reverse(rev,n);
    for(int i=0;i<n;++i) if(i<rev[i]) swap(a[i],a[rev[i]]);

    for(int len=2;len<=n;len<<=1){
        int m=len>>1;
        u32 wn=mod_pow(g,(br.mod-1)/len,br.mod);
        if(inverse) wn=mod_pow(wn,br.mod-2,br.mod);
        for(int i=0;i<n;i+=len){
            u32 w=1;
            for(int j=0;j<m;++j){
                u32 u=a[i+j];
                u32 v=br.mul(a[i+j+m],w);
                a[i+j]       = u+v>=br.mod?u+v-br.mod:u+v;
                a[i+j+m]     = u>=v?u-v:u+br.mod-v;
                w=br.mul(w,wn);
            }
        }
    }
    if(inverse){
        u32 inv_n=mod_pow(n,br.mod-2,br.mod);
        for(u32 &x:a) x=br.mul(x,inv_n);
    }
}

/* ------------ 多项式乘法 (Barrett NTT) -------- */
void poly_multiply(const int* a,const int* b,int* ab,int n,int p){
    Barrett br(p);
    int lim=1; while(lim<2*n) lim<<=1;
    vector<u32> A(lim,0),B(lim,0);
    for(int i=0;i<n;++i){ A[i]=((a[i]%p)+p)%p; B[i]=((b[i]%p)+p)%p; }

    ntt(A,false,br);
    ntt(B,false,br);
    for(int i=0;i<lim;++i) A[i]=br.mul(A[i],B[i]);
    ntt(A,true,br);

    for(int i=0;i<2*n-1;++i) ab[i]=static_cast<int>(A[i]);
}

/* ------------ 全局静态缓冲区 ------------------ */
static int a[300000],b[300000],ab[600000];

/* -------------------- main -------------------- */
int main(int argc,char* argv[]){
    MPI_Init(&argc,&argv);
    int rank,size;
    MPI_Comm_rank(MPI_COMM_WORLD,&rank);
    MPI_Comm_size(MPI_COMM_WORLD,&size);

    const int first = 0;   /* 根据需要调整 */
    const int last  = 3;   /* ↑↑↑↑↑↑↑↑↑↑↑ */
    if(rank==0){
        cout << "MPI case-level parallel NTT, ranks = " << size << '\n';
        cout.flush();
    }

    /* 轮流处理自己负责的用例 ------------------------------------- */
    for(int id=first; id<=last; ++id){
        if( (id-first) % size != rank ) continue;   // 非本进程跳过

        int n,p;
        fRead(a,b,&n,&p,id);

        auto t0 = chrono::high_resolution_clock::now();
        poly_multiply(a,b,ab,n,p);
        auto t1 = chrono::high_resolution_clock::now();
        double us = chrono::duration<double,std::micro>(t1-t0).count();

        /* 验证并输出 */
        cout << "[rank " << rank << "] ";
        fCheck(ab,n,id);
        cout << "[rank " << rank << "] latency for n="<<n<<" p="<<p<<" : "
             << us << " us\n";
        cout.flush();

        /* 如果需要把结果写文件，取消下一行 */
        // fWrite(ab,n,id);
    }

    MPI_Barrier(MPI_COMM_WORLD);
    if(rank==0) cout << "全部用例处理完成。\n";

    MPI_Finalize();
    return 0;
}
