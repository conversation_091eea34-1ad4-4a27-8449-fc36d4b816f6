/****************************************************************************************
 * barrett_radix2_simd.cc - SIMD (NEON) optimized Radix-2 NTT with Barrett reduction
 *
 * Based on main_barrett_radix2_ntt.cc
 ****************************************************************************************/
#include <vector>
#include <string>
#include <iostream>
#include <cstdint>
#include <numeric> // For std::iota if used in testing
#include <algorithm> // For std::swap, std::all_of if used in testing
#include <fstream> // For file operations

#ifdef __ARM_NEON
#include <arm_neon.h>
#endif

using u32  = uint32_t;
using u64  = uint64_t;
// u128 might not be directly used in NEON version if high-multiply is available
#if defined(_MSC_VER) && !defined(__clang__) && !defined(__GNUC__)
// MSVC specific 128-bit type if absolutely needed for scalar fallback comparison
// However, for NEON focus, we'd rely on NEON's capabilities for 64x64->high_64
// For cross-platform code that might fall back, this could be relevant.
// For pure NEON on ARM, __uint128_t is standard with GCC/Clang.
// Let's assume GCC/Clang environment for __uint128_t if a scalar version of Barrett is kept for comparison.
    #if defined(__GNUC__) || defined(__clang__)
        using u128 = __uint128_t;
    #else
        // Placeholder if u128 is strictly needed and not on GCC/Clang/MSVC_that_supports_it
        // This SIMD version aims to avoid direct u128 arithmetic in the hot loops.
        struct u128 { uint64_t lo, hi; }; // Dummy for non-supporting compilers if scalar Barrett is included
    #endif
#elif defined(__GNUC__) || defined(__clang__)
using u128 = __uint128_t;
#else
// Fallback for other compilers if u128 is absolutely needed for scalar Barrett.
// Again, SIMD target is to avoid this in critical path.
struct u128 { uint64_t lo, hi; }; 
#endif


/**
 * @brief Barrett reduction class.
 * 
 * Provides methods for modular arithmetic using Barrett reduction.
 */
class Barrett {
public:
    /**
     * @brief Construct a new Barrett object.
     * @param m The modulus.
     */
    explicit Barrett(u32 m): mod(m) {
        if (m == 0) {
            // Handle error: modulus cannot be zero.
            // For simplicity, let's assume m is always a valid prime for NTT.
            // In a real scenario, throw an exception or handle error.
            inv = 0; // Should not happen with valid NTT modulus
            return;
        }
        inv = (static_cast<u128>(1) << 64) / m;
    }

    /**
     * @brief Reduces a 64-bit integer modulo mod.
     * @param x The 64-bit integer to reduce.
     * @return u32 The result of x mod mod.
     */
    inline u32 reduce(u64 x) const {
        if (mod == 0) return static_cast<u32>(x); // Should not happen
        u64 q = (static_cast<u128>(x) * inv) >> 64;
        u64 r = x - q * mod;
        // The result of x - q*mod can be in [0, 2*mod - 1]
        // A single subtraction is usually sufficient if inv is calculated carefully.
        // Standard Barrett might require two: if (r >= mod) r-=mod; if (r >= mod) r-=mod;
        // Or, the common form:
        if (r >= mod) r -= mod; // r is now in [0, mod-1] or [mod, 2*mod-1] if previous r was > mod
                                // if r was already < mod, this does nothing.
                                // if r was [mod, 2*mod-1], it becomes [0, mod-1]
                                // A more robust version checks r >= mod again or uses a conditional move.
                                // However, the typical one conditional subtraction is common.
                                // For NTT, values are often positive.
        return static_cast<u32>(r);
    }

    /**
     * @brief Computes (a * b) mod mod.
     * @param a First operand.
     * @param b Second operand.
     * @return u32 The result of (a * b) mod mod.
     */
    inline u32 mul(u32 a, u32 b) const {
        return reduce(static_cast<u64>(a) * b);
    }

    const u32 mod; // Public to allow access by SIMD functions if needed
//private: // inv made public for potential SIMD setup
public: 
    u64 inv; 
};

/**
 * @brief Computes (a^e) mod mod_val.
 * @param a Base.
 * @param e Exponent.
 * @param mod_val Modulus.
 * @return u32 Result of modular exponentiation.
 */
static u32 mod_pow(u32 a, u64 e, u32 mod_val) { // Renamed mod to mod_val to avoid conflict
    u64 res = 1;
    u64 base = a;
    base %= mod_val;
    while (e > 0) {
        if (e % 2 == 1) res = (static_cast<u128>(res) * base) % mod_val;
        base = (static_cast<u128>(base) * base) % mod_val;
        e /= 2;
    }
    return static_cast<u32>(res);
}

/**
 * @brief Performs bit-reversal permutation on a vector.
 * @param a Vector to permute.
 */
static void bit_reverse_permute(std::vector<u32>& a) {
    int n = a.size();
    if (n == 0) return;
    int lg = 0;
    while ((1 << lg) < n) {
        lg++;
    }
    if ((1 << lg) != n && n != 0) {
        // Error or handle non-power-of-2 size if necessary
        // For classic NTT, n is power of 2.
        if (n>0) std::cerr << "Warning: bit_reverse_permute input size " << n << " is not a power of 2." << std::endl;
        // Fallback to no permutation or error
        return; 
    }
    if (n==0) return; // Already handled, but defensive

    std::vector<int> rev(n);
    rev[0] = 0; // Optimization: rev[0] is always 0
    for (int i = 1; i < n; ++i) { // Start from 1
        rev[i] = (rev[i >> 1] >> 1) | ((i & 1) << (lg - 1));
    }

    for (int i = 0; i < n; ++i) {
        if (i < rev[i]) {
            std::swap(a[i], a[rev[i]]);
        }
    }
}


/**
 * @brief Scalar Radix-2 Number Theoretic Transform (NTT).
 * @param a Input/output vector.
 * @param inverse If true, performs inverse NTT.
 * @param br Barrett reduction object.
 * @param g Primitive root of unity (typically 3).
 */
void ntt_scalar(std::vector<u32>& a, bool inverse, const Barrett& br, u32 g = 3) {
    int n = a.size();
    if (n == 0 || (n & (n - 1)) != 0) { // Check for n=0 or not power of 2
        if (n!=0) std::cerr << "NTT size must be a power of 2. Got " << n << std::endl;
        return;
    }

    bit_reverse_permute(a);

    for (int len = 2; len <= n; len <<= 1) {
        int m = len >> 1;
        u32 wn = mod_pow(g, (br.mod - 1) / len, br.mod);
        if (inverse) {
            wn = mod_pow(wn, br.mod - 2, br.mod);
        }
        for (int i = 0; i < n; i += len) {
            u32 w = 1;
            for (int j = 0; j < m; ++j) {
                u32 u = a[i + j];
                u32 v = br.mul(a[i + j + m], w);
                a[i + j]     = (u + v >= br.mod) ? (u + v - br.mod) : (u + v);
                a[i + j + m] = (u >= v) ? (u - v) : (u + br.mod - v);
                w = br.mul(w, wn);
            }
        }
    }

    if (inverse) {
        u32 inv_n = mod_pow(n, br.mod - 2, br.mod);
        for (u32 &x : a) {
            x = br.mul(x, inv_n);
        }
    }
}

/**
 * @brief NEON SIMD Radix-2 Number Theoretic Transform (NTT).
 * @param a Input/output vector.
 * @param inverse If true, performs inverse NTT.
 * @param br Barrett reduction object.
 * @param g Primitive root of unity (typically 3 for forward, its inverse for INTT).
 */
void ntt_neon(std::vector<u32>& a, bool inverse, const Barrett& br, u32 g = 3);


/**
 * @brief Reads n, p, and one polynomial 'a' from nttdata/[id].in file.
 * @param vec_a Output vector for the polynomial.
 * @param n_val Output for n (size of polynomial).
 * @param p_val Output for p (modulus).
 * @param id Test case ID to construct filename.
 * @return true If successful, false otherwise.
 */
bool fRead_poly(std::vector<u32>& vec_a, u32& n_val, u32& p_val, int id) {
    std::string path = "nttdata/" + std::to_string(id) + ".in";
    std::ifstream fin(path);
    if (!fin) {
        std::cerr << "Error: Cannot open input file " << path << std::endl;
        return false;
    }
    int temp_n_int, temp_p_int;
    fin >> temp_n_int >> temp_p_int;
    n_val = static_cast<u32>(temp_n_int);
    p_val = static_cast<u32>(temp_p_int);

    if (n_val == 0 || (n_val & (n_val - 1)) != 0) {
        std::cerr << "Error: N from file (" << n_val << ") is not a power of 2 or is zero. File: " << path << std::endl;
        return false;
    }

    vec_a.resize(n_val);
    for (u32 i = 0; i < n_val; ++i) {
        int val;
        if (!(fin >> val)) {
            std::cerr << "Error reading polynomial data from " << path << std::endl;
            return false;
        }
        vec_a[i] = (static_cast<long long>(val) % p_val + p_val) % p_val; // Ensure positive modulo
    }
    // Skip the second polynomial if present in the file
    fin.close();
    return true;
}

// Main function for testing
int main() {
    for (int id = 0; id <= 3; ++id) {
        std::cout << "--- Processing Test ID: " << id << " --- " << std::endl;
        u32 n_val, p_val;
        std::vector<u32> poly_orig;

        if (!fRead_poly(poly_orig, n_val, p_val, id)) {
            std::cout << "Failed to read test case " << id << std::endl << std::endl;
            continue;
        }

        Barrett br(p_val);
        std::cout << "N = " << n_val << ", P = " << p_val << std::endl;
        std::cout << "Original (first 16 elements): ";
        for(size_t i=0; i < std::min((size_t)16, poly_orig.size()); ++i) std::cout << poly_orig[i] << " ";
        std::cout << (poly_orig.size() > 16 ? "..." : "");
        std::cout << std::endl << std::endl;

        std::vector<u32> poly_neon = poly_orig;
        std::vector<u32> poly_scalar = poly_orig;

        // Test scalar version
        std::cout << "--- Scalar Version --- " << std::endl;
        ntt_scalar(poly_scalar, false, br);
        std::cout << "Scalar NTT (first 16): ";
        for(size_t i=0; i < std::min((size_t)16, poly_scalar.size()); ++i) std::cout << poly_scalar[i] << " ";
        std::cout << (poly_scalar.size() > 16 ? "..." : "");
        std::cout << std::endl;
        ntt_scalar(poly_scalar, true, br);
        std::cout << "Scalar INTT (should be original, first 16): ";
        for(size_t i=0; i < std::min((size_t)16, poly_scalar.size()); ++i) std::cout << poly_scalar[i] << " ";
        std::cout << (poly_scalar.size() > 16 ? "..." : "");
        std::cout << std::endl << std::endl;

    #ifdef __ARM_NEON
        std::cout << "--- NEON Version --- " << std::endl;
        ntt_neon(poly_neon, false, br);
        std::cout << "NEON NTT (first 16): ";
        for(size_t i=0; i < std::min((size_t)16, poly_neon.size()); ++i) std::cout << poly_neon[i] << " ";
        std::cout << (poly_neon.size() > 16 ? "..." : "");
        std::cout << std::endl;
        ntt_neon(poly_neon, true, br);
        std::cout << "NEON INTT (should be original, first 16): ";
        for(size_t i=0; i < std::min((size_t)16, poly_neon.size()); ++i) std::cout << poly_neon[i] << " ";
        std::cout << (poly_neon.size() > 16 ? "..." : "");
        std::cout << std::endl << std::endl;

        // Verification
        bool match = true;
        if (poly_scalar.size() != poly_neon.size()) {
            match = false;
            std::cout << "Size mismatch after INTT! Scalar size: " << poly_scalar.size() << ", NEON size: " << poly_neon.size() << std::endl;
        } else if (poly_orig.size() != poly_scalar.size()) {
             match = false;
             std::cout << "Size mismatch! Original size: " << poly_orig.size() << ", Scalar/NEON INTT size: " << poly_scalar.size() << std::endl;
        }else {
            for(size_t i=0; i<poly_orig.size(); ++i) { 
                if (poly_scalar[i] != poly_orig[i]) { 
                    match = false;
                    std::cout << "MISMATCH (Scalar vs Original) at index " << i << ": Original=" << poly_orig[i] << ", Scalar=" << poly_scalar[i] << std::endl;
                }
                if (poly_neon[i] != poly_orig[i]) { 
                    match = false;
                    std::cout << "MISMATCH (NEON vs Original) at index " << i << ": Original=" << poly_orig[i] << ", NEON=" << poly_neon[i] << std::endl;
                }
            }
        }
        if(match) {
            std::cout << "SUCCESS: All versions match original after INTT for ID " << id << "!" << std::endl;
        } else {
            std::cout << "FAILURE: Mismatch detected for ID " << id << "!" << std::endl;
        }
        std::cout << "---------------------------------" << std::endl << std::endl;

    #else
        std::cout << "NEON not available/enabled in this build. Skipping NEON tests for ID " << id << "." << std::endl;
        std::cout << "---------------------------------" << std::endl << std::endl;
    #endif
    }
    return 0;
}

// Definition of ntt_neon will follow
#ifdef __ARM_NEON

/**
 * @brief Vectorized Barrett modular multiplication for four u32 pairs.
 * 
 * Performs (val_a[k] * val_b[k]) mod br_mod for k=0..3 using NEON intrinsics.
 * Relies on AArch64 specific intrinsics (like vmulh_u64, vmulq_u64) for efficient 
 * Barrett reduction steps. Falls back to scalar operations if these are not available.
 * 
 * @param val_a Input vector a.
 * @param val_b Input vector b.
 * @param br_mod Modulus.
 * @param br_inv Precomputed Barrett inverse ((1<<64)/modulus).
 * @param br_obj_ref Reference to Barrett object for scalar fallback.
 * @return uint32x4_t Vector of 4 results.
 */
inline uint32x4_t barrett_mul_u32x4(uint32x4_t val_a, uint32x4_t val_b, u32 br_mod, u64 br_inv, const Barrett& br_obj_ref) {
    uint32x2_t a_lo = vget_low_u32(val_a);
    uint32x2_t a_hi = vget_high_u32(val_a);
    uint32x2_t b_lo = vget_low_u32(val_b);
    uint32x2_t b_hi = vget_high_u32(val_b);

    uint64x2_t x_lo = vmull_u32(a_lo, b_lo); 
    uint64x2_t x_hi = vmull_u32(a_hi, b_hi); 

    #if defined(__aarch64__) && defined(__ARM_FEATURE_SHA512) 
        uint64x2_t inv_vec_duplicated = vdupq_n_u64(br_inv);
        uint64x2_t q_lo = vmulh_u64(x_lo, inv_vec_duplicated);
        uint64x2_t q_hi = vmulh_u64(x_hi, inv_vec_duplicated);

        uint64x2_t mod_u64_duplicated = vdupq_n_u64(br_mod);
        uint64x2_t q_mod_lo = vmulq_u64(q_lo, mod_u64_duplicated);
        uint64x2_t q_mod_hi = vmulq_u64(q_hi, mod_u64_duplicated);

        uint64x2_t r_lo = vsubq_u64(x_lo, q_mod_lo);
        uint64x2_t r_hi = vsubq_u64(x_hi, q_mod_hi);

        uint64x2_t mask_lo = vcgeq_u64(r_lo, mod_u64_duplicated);
        r_lo = vbslq_u64(mask_lo, vsubq_u64(r_lo, mod_u64_duplicated), r_lo);
        
        uint64x2_t mask_hi = vcgeq_u64(r_hi, mod_u64_duplicated);
        r_hi = vbslq_u64(mask_hi, vsubq_u64(r_hi, mod_u64_duplicated), r_hi);

        uint32x2_t res_narrow_lo = vmovn_u64(r_lo);
        uint32x2_t res_narrow_hi = vmovn_u64(r_hi);
        return vcombine_u32(res_narrow_lo, res_narrow_hi);
    #else 
        u32 results[4];
        u32 current_a[4], current_b[4];
        vst1q_u32(current_a, val_a);
        vst1q_u32(current_b, val_b);
        for(int k=0; k<4; ++k) {
            results[k] = br_obj_ref.mul(current_a[k], current_b[k]);
        }
        return vld1q_u32(results);
    #endif
}

void ntt_neon(std::vector<u32>& a, bool inverse, const Barrett& br_obj, u32 g) {
    int n = a.size();
    if (n == 0 || (n & (n - 1)) != 0) { 
         if (n!=0) std::cerr << "NEON NTT size must be a power of 2. Got " << n << std::endl;
        return;
    }
    // Fallback for n < 4 (vector_size) can remain as scalar call
    if (n < 4 && n > 0) { 
        ntt_scalar(a, inverse, br_obj, g);
        return;
    }

    bit_reverse_permute(a);

    u32 br_mod = br_obj.mod;
    u64 br_inv = br_obj.inv; 
    uint32x4_t mod_vec = vdupq_n_u32(br_mod);

    for (int len = 2; len <= n; len <<= 1) {
        int m = len >> 1;
        u32 wn_scalar = mod_pow(g, (br_mod - 1) / len, br_mod);
        if (inverse) {
            wn_scalar = mod_pow(wn_scalar, br_mod - 2, br_mod);
        }

        for (int i = 0; i < n; i += len) {
            u32 w_scalar = 1;
            int j = 0;
            for (; j + 3 < m; j += 4) { 
                uint32x4_t u_vec = vld1q_u32(&a[i + j]);
                uint32x4_t v_input_vec = vld1q_u32(&a[i + j + m]);

                uint32_t w_terms_scalar[4];
                w_terms_scalar[0] = w_scalar;
                w_terms_scalar[1] = br_obj.mul(w_terms_scalar[0], wn_scalar);
                w_terms_scalar[2] = br_obj.mul(w_terms_scalar[1], wn_scalar);
                w_terms_scalar[3] = br_obj.mul(w_terms_scalar[2], wn_scalar);
                uint32x4_t w_vec = vld1q_u32(w_terms_scalar);
                
                uint32x4_t v_vec = barrett_mul_u32x4(v_input_vec, w_vec, br_mod, br_inv, br_obj);
                
                uint32x4_t u_plus_v = vaddq_u32(u_vec, v_vec);
                uint32x4_t u_minus_v = vsubq_u32(u_vec, v_vec);

                uint32x4_t mask_add = vcgeq_u32(u_plus_v, mod_vec);
                u_plus_v = vbslq_u32(mask_add, vsubq_u32(u_plus_v, mod_vec), u_plus_v);
                
                uint32x4_t mask_sub_neg = vcltq_s32(vreinterpretq_s32_u32(u_minus_v), vdupq_n_s32(0)); 
                u_minus_v = vbslq_u32(mask_sub_neg, vaddq_u32(u_minus_v, mod_vec), u_minus_v);

                vst1q_u32(&a[i + j], u_plus_v);
                vst1q_u32(&a[i + j + m], u_minus_v);

                w_scalar = br_obj.mul(w_terms_scalar[3], wn_scalar);
            }
            for (; j < m; ++j) {
                u32 u = a[i + j];
                u32 v = br_obj.mul(a[i + j + m], w_scalar);
                a[i + j]     = (u + v >= br_mod) ? (u + v - br_mod) : (u + v);
                a[i + j + m] = (u >= v) ? (u - v) : (u + br_mod - v);
                w_scalar = br_obj.mul(w_scalar, wn_scalar);
            }
        }
    }

    if (inverse) {
        u32 inv_n_scalar = mod_pow(n, br_mod - 2, br_mod);
        for (u32 &x : a) { 
            x = br_obj.mul(x, inv_n_scalar);
        }
    }
}
#endif // __ARM_NEON
