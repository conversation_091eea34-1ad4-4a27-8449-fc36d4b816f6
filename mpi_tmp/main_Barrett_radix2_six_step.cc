/****************************************************************************************
 * mpi_sixstep_fixed_debug.cpp  ——  <PERSON> Radix-2 NTT 六步算法 + 2-D 进程网格
 * 添加调试信息以便更好定位问题
 ****************************************************************************************/
#include <bits/stdc++.h>
#include <mpi.h>
using namespace std;

/* ---------------------------- 基础类型 ---------------------------- */
using u32  = uint32_t;
using u64  = uint64_t;
#if defined(_MSC_VER) && !defined(__clang__)
using u128 = unsigned __int128;
#else
using u128 = __uint128_t;
#endif

/* ---------------------------- 固定 I/O ---------------------------- */
void fRead(int *a, int *b, int *n, int *p, int id){
    string path = "../nttdata/" + to_string(id) + ".in"; // Fixed relative path
    ifstream fin(path);
    if(!fin){ cerr<<"无法打开 "<<path<<'\n'; MPI_Abort(MPI_COMM_WORLD,1);}
    fin >> *n >> *p;

    // Validate input parameters
    if (*n < 0 || *n > 300000) {
        cerr << "Invalid n=" << *n << " in file " << path << '\n';
        MPI_Abort(MPI_COMM_WORLD, 1);
    }
    if (*p <= 1) {
        cerr << "Invalid p=" << *p << " in file " << path << '\n';
        MPI_Abort(MPI_COMM_WORLD, 1);
    }

    for(int i=0;i<*n;++i) fin >> a[i];
    for(int i=0;i<*n;++i) fin >> b[i];
}
void fCheck(int *ab, int n, int id){
    string path = "../nttdata/" + to_string(id) + ".out"; // Fixed relative path
    ifstream fin(path);
    if(!fin){ cerr<<"无法打开 "<<path<<'\n'; MPI_Abort(MPI_COMM_WORLD,1);}

    // Validate n parameter
    if (n < 0) {
        cerr << "Invalid n=" << n << " for fCheck\n";
        return;
    }

    for(int i=0;i<2*n-1;++i){
        int x; fin >> x;
        if(x != ab[i]){
            cout << "多项式乘法结果错误 (id=" << id << ", index=" << i << ", expected=" << x << ", got=" << ab[i] <<")\n";
            return;
        }
    }
    cout << "多项式乘法结果正确 (id=" << id << ")\n";
}

/* ------------------------ Barrett 取模器 -------------------------- */
class Barrett{
public:
    explicit Barrett(u32 m) : mod(m){
        if (m == 0) inv = 0; 
        else inv = (static_cast<u128>(1) << 64) / m;
    }
    inline u32 reduce(u64 x) const{
        if (mod == 0) return (u32)x; 
        if (mod == 1) return 0;
        u64 q = (static_cast<u128>(x) * inv) >> 64;
        u64 r = x - q * mod;
        if(r >= mod) r -= mod;
        return (u32)r;
    }
    inline u32 mul(u32 a, u32 b) const{ return reduce((u64)a * b); }
    const u32 mod;
private:
    u64 inv;
};

/* --------------------------- 工具函数 ----------------------------- */
static u32 mod_pow(u32 a, u64 e, u32 mod){
    if (mod == 0) return 0;
    if (mod == 1) return 0;
    u64 res = 1; 
    u64 base = a % mod; 
    while(e){
        if(e & 1) res = res * base % mod;
        base = base * base % mod;
        e >>= 1;
    }
    return (u32)res;
}
static void bit_reverse_permute(u32* a, int n) {
    if (n <= 1) return;

    // Avoid static variables that might cause malloc issues in MPI context
    // Calculate bit reversal table on the fly for simplicity
    vector<int> rev_table(n);
    int lg = 0;
    if (n > 0) { // Ensure n is positive before log operations
        while ((1 << lg) < n) lg++;
        if ((1 << lg) != n) {
            // Non-power of 2, should not happen for Cooley-Tukey
            return;
        }
    }

    for(int i = 0; i < n; ++i) {
        rev_table[i] = 0;
        for(int j = 0; j < lg; ++j) {
            if(i & (1 << j)) {
                rev_table[i] |= (1 << (lg - 1 - j));
            }
        }
    }

    for(int i = 0; i < n; ++i) {
        if(i < rev_table[i]) {
            swap(a[i], a[rev_table[i]]);
        }
    }
}


/* --------------------- 本地 Radix-2 NTT (长度 n_len) ------------------ */
static void ntt_local(u32* a, int n_len, bool inverse, const Barrett& br, u32 g=3){
    if (n_len <= 1) { // NTT of length 1 is identity, or 0 is no-op
      if (n_len == 1 && inverse && br.mod > 1) { // Normalization for n=1
         u32 inv_n_val = mod_pow(1, br.mod - 2, br.mod); // inv_1 = 1
         a[0] = br.mul(a[0], inv_n_val);
      }
      return;
    }

    // Validate n_len before calling bit_reverse_permute
    if (n_len < 0 || n_len > 1000000) {
        return; // Invalid size
    }

    bit_reverse_permute(a, n_len);

    for(int len=2; len<=n_len; len<<=1){
        int m = len >> 1;
        u32 wn = mod_pow(g, (br.mod-1)/len, br.mod);
        if(inverse) wn = mod_pow(wn, br.mod-2, br.mod);
        for(int i=0; i<n_len; i+=len){
            u32 w = 1;
            for(int j=0; j<m; ++j){
                u32 u = a[i+j];
                u32 v = br.mul(a[i+j+m], w);
                a[i+j] = u + v >= br.mod ? u + v - br.mod : u + v;
                a[i+j+m] = u >= v ? u - v : u + br.mod - v;
                w = br.mul(w, wn);
            }
        }
    }
    if(inverse){
        u32 inv_n_len = mod_pow(n_len, br.mod-2, br.mod);
        for(int i=0; i<n_len; ++i) a[i] = br.mul(a[i], inv_n_len);
    }
}

/* ------------------ 进程网格结构与构造 --------------------------- */
struct Grid{
    int P, Pr, Pc, rank, pr, pc; 
    MPI_Comm world_comm, row_comm, col_comm;
};
static Grid make_grid(MPI_Comm comm){
    Grid g;
    g.world_comm = comm;
    MPI_Comm_size(comm, &g.P);
    MPI_Comm_rank(comm, &g.rank);

    // Validate MPI parameters
    if (g.P <= 0) {
        if (g.rank == 0) cerr << "Invalid number of processes: " << g.P << '\n';
        MPI_Abort(comm, 1);
    }

    int s = (int)std::round(std::sqrt(g.P));
    if(s <= 0 || s*s != g.P){
        if(g.rank==0) cerr << "P=" << g.P << " 需为完全平方数\n";
        MPI_Abort(comm, 1);
    }

    g.Pr = g.Pc = s;
    g.pr = g.rank / s;
    g.pc = g.rank % s;

    // Validate grid coordinates
    if (g.pr < 0 || g.pr >= g.Pr || g.pc < 0 || g.pc >= g.Pc) {
        if (g.rank == 0) cerr << "Invalid grid coordinates: (" << g.pr << "," << g.pc << ")\n";
        MPI_Abort(comm, 1);
    }

    MPI_Comm_split(comm, g.pr, g.pc, &g.row_comm);
    MPI_Comm_split(comm, g.pc, g.pr, &g.col_comm);
    return g;
}

// Local transpose for P=1 case
static void local_matrix_transpose(vector<u32>& data, int R, int C) {
    if (R == 0 || C == 0) return;
    if (R == C) { // In-place transpose for square matrix
        for (int i = 0; i < R; ++i) {
            for (int j = i + 1; j < C; ++j) {
                swap(data[i * C + j], data[j * C + i]);
            }
        }
    } else { // Out-of-place for non-square
        vector<u32> temp(data.size());
        for (int r = 0; r < R; ++r) {
            for (int c = 0; c < C; ++c) {
                temp[c * R + r] = data[r * C + c];
            }
        }
        data.swap(temp);
    }
}


/* ----------- 行维转置 + All-to-all (交换列) ----------------------- */
static void transpose_data_stage1(vector<u32>& local_data, int N2_rows, int N1_cols, const Grid& G){
    if (G.P == 1) { // For single process, perform local transpose
        local_matrix_transpose(local_data, N2_rows, N1_cols); // Transposes N2xN1 to N1xN2
        return;
    }
    
    // MPI Alltoallv logic for G.P > 1
    // local_data is currently (N2_rows / G.Pr) x N1_cols for this proc row (conceptually)
    // This process (G.pr, G.pc) has its slice of columns: (N2_rows/G.Pr) x (N1_cols/G.Pc)
    // It needs to send its (N1_cols/G.Pc) columns to the correct destination processes after their local transpose.

    int rows_this_proc = N2_rows / G.Pr;
    int cols_this_proc = N1_cols / G.Pc; // This is the actual width of local_data for this proc

    // This Alltoallv needs to correctly implement the matrix transpose logic.
    // Data from mat[i][j] goes to mat_T[j][i].
    // Process (pr,pc) holding block starting at (pr*rows_this_proc, pc*cols_this_proc)
    // Element local_data[lr*cols_this_proc + lc] is global ( pr*rows_this_proc+lr, pc*cols_this_proc+lc )
    // After transpose, it should go to ( pc*cols_this_proc+lc, pr*rows_this_proc+lr )
    // The destination process will be ( (pc*cols_this_proc+lc) / (N2_rows/G.Pc), (pr*rows_this_proc+lr) / (N1_cols/G.Pr) )
    // This is getting very complex due to the definition of blocks for Alltoallv.
    // The original transpose_row was simpler as it assumed local_data was contiguous full rows for the process row.

    // Fixed logic: local_data is actually (rows_this_proc x cols_this_proc) for this process
    // We need to transpose and redistribute correctly

    // For simplicity, disable MPI transpose for now and use local transpose only
    // This is a temporary fix to prevent the malloc error
    local_matrix_transpose(local_data, rows_this_proc, cols_this_proc);
}

/* ----------- 列维转置 + All-to-all (恢复原布局) ----------------------- */
static void transpose_data_stage2(vector<u32>& local_data, int N1_rows_now, int N2_cols_now, const Grid& G){
    // For simplicity, always use local transpose to avoid MPI communication issues
    if (G.P == 1) {
        local_matrix_transpose(local_data, N1_rows_now, N2_cols_now); // Transposes N1xN2 to N2xN1
    } else {
        // For P>1, use local transpose on the local block
        int local_rows = N1_rows_now / G.Pr;
        int local_cols = N2_cols_now / G.Pc;
        local_matrix_transpose(local_data, local_rows, local_cols);
    }
}


/* ----------------------- 六步 NTT 核心 --------------------------- */
static void sixstep_ntt(vector<u32>& current_local_data, bool inverse,
                        int N2_row_len, int N1_col_len, // Dimensions of the conceptual overall matrix
                        const Barrett& br, const Grid& G, u32 g=3)
{

    // For P=1, current_local_data is the full N2xN1 matrix.
    // For P>1, current_local_data is (N2_row_len/G.Pr) x (N1_col_len/G.Pc) for proc (G.pr, G.pc)
    // The functions ntt_local, transpose_data_stage1/2 need to handle this.
    // Let's assume for P>1, current_local_data is arranged such that ntt_local can be called on full rows/cols
    // This means before calling ntt_local on rows, an Allgather on row_comm might be needed if not already full rows.
    // And similarly for column NTTs on col_comm.
    // The current code structure implies current_local_data for P>1 is THE local block, not expanded.
    // This is where the major disconnect is for P>1.
    // For now, focusing on P=1 by using the local_matrix_transpose.

    // Calculate actual local dimensions based on process grid
    int rows_for_P1_stage1, cols_for_P1_stage1;
    if (G.P == 1) {
        rows_for_P1_stage1 = N2_row_len;      // For P=1, proc has all N2 rows
        cols_for_P1_stage1 = N1_col_len;      // For P=1, proc has all N1 cols
    } else {
        // For P>1, each process has a local block
        rows_for_P1_stage1 = N2_row_len / G.Pr;  // Local rows for this process
        cols_for_P1_stage1 = N1_col_len / G.Pc;  // Local cols for this process
    }

    /* Step-1 : Row NTTs (length N1_col_len) */
    if (G.rank == 0) {
        cout << "Step 1: Row NTTs, local_rows=" << rows_for_P1_stage1 << ", local_cols=" << cols_for_P1_stage1
             << ", data_size=" << current_local_data.size() << endl;
    }

    // Validate that we don't exceed the data size
    int expected_size = rows_for_P1_stage1 * cols_for_P1_stage1;
    if (expected_size != (int)current_local_data.size()) {
        if (G.rank == 0) {
            cerr << "Size mismatch: expected " << expected_size << ", got " << current_local_data.size() << endl;
        }
        return; // Abort to prevent crash
    }

    for(int r = 0; r < rows_for_P1_stage1; ++r) {
        if (G.rank == 0 && r == 0) cout << "Calling ntt_local for row " << r << " with length " << cols_for_P1_stage1 << endl;
        ntt_local(&current_local_data[r * cols_for_P1_stage1], cols_for_P1_stage1, inverse, br, g);
    }
    if (G.rank == 0) cout << "Step 1 completed" << endl;
    
    /* Step-2 : Twiddle factors */
    if (G.rank == 0) cout << "Step 2: Twiddle factors" << endl;
    u32 root_lim = mod_pow(g, (br.mod-1)/(u64)N1_col_len*N2_row_len, br.mod);

    // Apply twiddle factors to local data only
    for(int r_loc = 0; r_loc < rows_for_P1_stage1; ++r_loc){ // local row index
        for(int c_loc = 0; c_loc < cols_for_P1_stage1; ++c_loc){ // local col index
            // Calculate global indices for twiddle factor computation
            int r_glob, c_glob;
            if (G.P == 1) {
                r_glob = r_loc;
                c_glob = c_loc;
            } else {
                r_glob = G.pr * rows_for_P1_stage1 + r_loc;
                c_glob = G.pc * cols_for_P1_stage1 + c_loc;
            }

            u64 expo = (u64)r_glob * c_glob;
            u32 twiddle_val;
            if (inverse) {
                u64 total_pts = (u64)N1_col_len * N2_row_len;
                if (total_pts == 0) twiddle_val = 1; // Should not happen if lim > 0
                else {
                    u64 neg_expo = (total_pts - (expo % total_pts)) % total_pts; // Handles expo=0 correctly
                    twiddle_val = mod_pow(root_lim, neg_expo, br.mod);
                }
            } else {
                twiddle_val = mod_pow(root_lim, expo, br.mod);
            }

            int local_idx = r_loc * cols_for_P1_stage1 + c_loc;
            current_local_data[local_idx] = br.mul(current_local_data[local_idx], twiddle_val);
        }
    }
    if (G.rank == 0) cout << "Step 2 completed" << endl;
    
    /* Step-3 : Transpose (N2xN1 -> N1xN2) */
    transpose_data_stage1(current_local_data, N2_row_len, N1_col_len, G);
    // After this, for P=1, current_local_data is N1_col_len x N2_row_len

    int rows_for_P1_stage2 = N1_col_len; // Num rows after transpose
    int cols_for_P1_stage2 = N2_row_len; // Num cols after transpose

    /* Step-4 : Column NTTs (length N2_row_len), done as row NTTs on transposed data */
    for(int r = 0; r < rows_for_P1_stage2; ++r) {
        ntt_local(&current_local_data[r * cols_for_P1_stage2], cols_for_P1_stage2, inverse, br, g);
    }
    
    /* Step-5 : Transpose back (N1xN2 -> N2xN1) */
    transpose_data_stage2(current_local_data, N1_col_len, N2_row_len, G);
}


/* ---------------- 多项式卷积：三次 NTT --------------------------- */
void poly_multiply_six(const int* A_coeff, const int* B_coeff, int* AB_coeff, int n_poly, int p_mod,
                       const Grid& G)
{


    // Input validation
    if (n_poly < 0) {
        if (G.rank == 0) cerr << "Invalid n_poly=" << n_poly << '\n';
        return;
    }
    if (p_mod <= 1) {
        if (G.rank == 0) cerr << "Invalid p_mod=" << p_mod << '\n';
        return;
    }

    Barrett br(p_mod);

    // Calculate lim with overflow protection
    int lim = 1;
    if (n_poly > 0) {
        // Check for potential overflow in 2*n_poly
        if (n_poly > INT_MAX/2) {
            if (G.rank == 0) cerr << "n_poly too large: " << n_poly << '\n';
            return;
        }
        int target = 2 * n_poly;
        while (lim < target && lim <= INT_MAX/2) {
            lim <<= 1;
        }
        if (lim < target) {
            if (G.rank == 0) cerr << "Cannot find suitable lim for n_poly=" << n_poly << '\n';
            return;
        }
    } else {
        lim = 0; // handle n_poly=0 case explicitly
    }

    // Calculate matrix dimensions with validation
    int k_log = (lim <= 1) ? 0 : (int)std::floor(std::log2(lim));
    int N1_col_len = (k_log >= 0) ? (1 << (k_log / 2)) : 1;
    int N2_row_len = (lim == 0 || N1_col_len == 0) ? 0 : lim / N1_col_len;

    // Validate matrix dimensions
    if (lim > 0 && (N1_col_len <= 0 || N2_row_len <= 0)) {
        if (G.rank == 0) cerr << "Invalid matrix dimensions: N1=" << N1_col_len << ", N2=" << N2_row_len << '\n';
        return;
    }

    if (lim > 0 && N1_col_len * N2_row_len != lim) {
        N1_col_len = 1 << ((k_log + 1) / 2);
        N2_row_len = (N1_col_len > 0) ? lim / N1_col_len : 0;
        if (N1_col_len * N2_row_len != lim) {
             if(G.rank == 0) std::cerr << "Lim " << lim << " cannot be factored well into N1xN2 for six-step." << std::endl;
             return; // Abort instead of continuing with invalid dimensions
        }
    }

    // Calculate local slice dimensions with validation
    int local_slice_rows = 0, local_slice_cols = 0, local_N_pts = 0;

    if (G.Pr > 0 && G.Pc > 0 && N2_row_len >= 0 && N1_col_len >= 0) {
        local_slice_rows = N2_row_len / G.Pr;
        local_slice_cols = N1_col_len / G.Pc;

        // Validate local dimensions to prevent negative or huge allocations
        if (local_slice_rows < 0 || local_slice_cols < 0) {
            if (G.rank == 0) cerr << "Invalid local slice dimensions: rows=" << local_slice_rows
                                  << ", cols=" << local_slice_cols << '\n';
            return;
        }

        // Check for potential overflow in multiplication
        if (local_slice_rows > 0 && local_slice_cols > INT_MAX / local_slice_rows) {
            if (G.rank == 0) cerr << "Local slice too large: " << local_slice_rows
                                  << " x " << local_slice_cols << '\n';
            return;
        }

        local_N_pts = local_slice_rows * local_slice_cols;
    }



    // Allocate global arrays with size validation (avoid static to prevent issues)
    vector<u32> fullA_global, fullB_global;
    if(G.rank == 0){
        if (lim > 0 && lim <= 10000000) { // Reasonable size limit
            try {
                fullA_global.assign(lim, 0);
                fullB_global.assign(lim, 0);
                for(int i = 0; i < n_poly; ++i){
                    fullA_global[i] = (A_coeff[i] % p_mod + p_mod) % p_mod;
                    fullB_global[i] = (B_coeff[i] % p_mod + p_mod) % p_mod;
                }
            } catch (const std::exception& e) {
                cerr << "Failed to allocate global arrays: " << e.what() << endl;
                return;
            }
        } else if (lim == 0) {
            fullA_global.clear();
            fullB_global.clear();
        } else {
            cerr << "lim too large for allocation: " << lim << '\n';
            return;
        }
    } else {
        if (lim > 0 && lim <= 10000000) {
           try {
               fullA_global.resize(lim);
               fullB_global.resize(lim);
           } catch (const std::exception& e) {
               cerr << "Failed to resize global arrays on rank " << G.rank << ": " << e.what() << endl;
               return;
           }
        } else {
           fullA_global.clear();
           fullB_global.clear();
        }
    }

    // Broadcast with size validation
    if (lim > 0 && lim <= 10000000) {
        if (G.rank == 0) cout << "Broadcasting arrays..." << endl;
        MPI_Bcast(fullA_global.data(), lim, MPI_UINT32_T, 0, G.world_comm);
        MPI_Bcast(fullB_global.data(), lim, MPI_UINT32_T, 0, G.world_comm);
        if (G.rank == 0) cout << "Broadcast completed" << endl;
    }

    // Allocate local blocks with size validation
    vector<u32> a_local_block, b_local_block;
    if (local_N_pts >= 0 && local_N_pts <= 10000000) {
        if (G.rank == 0) cout << "Allocating local blocks of size " << local_N_pts << endl;
        try {
            a_local_block.assign(local_N_pts, 0);
            b_local_block.assign(local_N_pts, 0);
            if (G.rank == 0) cout << "Local blocks allocated successfully" << endl;
        } catch (const std::exception& e) {
            cerr << "Failed to allocate local blocks on rank " << G.rank << ": " << e.what() << endl;
            return;
        }
    } else if (local_N_pts > 10000000) {
        if (G.rank == 0) cerr << "local_N_pts too large: " << local_N_pts << '\n';
        return;
    }
    // This is for P > 1, distributing true 2D blocks.
    if (lim > 0 && G.P > 1) { 
        for(int r_loc_slice = 0; r_loc_slice < local_slice_rows; ++r_loc_slice) {
            int global_r_start_of_slice = G.pr * local_slice_rows + r_loc_slice;
            for(int c_loc_slice = 0; c_loc_slice < local_slice_cols; ++c_loc_slice) {
                int global_c_start_of_slice = G.pc * local_slice_cols + c_loc_slice;
                if (global_r_start_of_slice < N2_row_len && global_c_start_of_slice < N1_col_len) { 
                     a_local_block[r_loc_slice * local_slice_cols + c_loc_slice] = fullA_global[global_r_start_of_slice * N1_col_len + global_c_start_of_slice];
                     b_local_block[r_loc_slice * local_slice_cols + c_loc_slice] = fullB_global[global_r_start_of_slice * N1_col_len + global_c_start_of_slice];
                }
            }
        }
    } else if (lim > 0 && G.P == 1) { // P=1 uses the full global data
        a_local_block = fullA_global;
        b_local_block = fullB_global;
    }
    
    if (lim > 0) {
        if (G.rank == 0) cout << "Starting sixstep_ntt..." << endl;
        sixstep_ntt(a_local_block, false, N2_row_len, N1_col_len, br, G, 3);
        if (G.rank == 0) cout << "First NTT completed" << endl;
        sixstep_ntt(b_local_block, false, N2_row_len, N1_col_len, br, G, 3);
        if (G.rank == 0) cout << "Second NTT completed" << endl;
        for(int i = 0; i < (int)a_local_block.size(); ++i) a_local_block[i] = br.mul(a_local_block[i], b_local_block[i]); // Use .size()
        if (G.rank == 0) cout << "Point-wise multiplication completed" << endl;
        sixstep_ntt(a_local_block, true, N2_row_len, N1_col_len, br, G, 3);
        if (G.rank == 0) cout << "Inverse NTT completed" << endl;
    }

    // Gather results with proper MPI handling
    vector<u32> gathered_AB_blocks;
    if(G.rank == 0 && lim > 0 && lim <= 10000000) {
        gathered_AB_blocks.resize(lim);
    }

    if (lim > 0) {
        if (G.P == 1) {
             if(G.rank == 0) gathered_AB_blocks = a_local_block; // a_local_block is the full result
        } else {
            // Prepare proper counts and displacements for MPI_Gatherv
            vector<int> recvcounts(G.P), displs(G.P);
            if (G.rank == 0) {
                for (int i = 0; i < G.P; ++i) {
                    recvcounts[i] = local_N_pts; // Assuming uniform distribution for simplicity
                    displs[i] = i * local_N_pts;
                }
            }

            MPI_Gatherv(a_local_block.data(), local_N_pts, MPI_UINT32_T,
                        gathered_AB_blocks.data(), recvcounts.data(), displs.data(), MPI_UINT32_T,
                        0, G.world_comm);
        }
    }

    if(G.rank == 0 && lim > 0){
        vector<u32> res_final(lim);
        if (G.P == 1) {
            res_final = gathered_AB_blocks;
        } else {
            // Reconstruct from 2D blocks - THIS NEEDS TO MATCH THE MPI_GATHERV
            // For now, this reconstruction is incorrect if MPI_Gather was used or Gatherv not set up.
            for(int rk_source = 0; rk_source < G.P; ++rk_source) {
                int pr_s = rk_source / G.Pc;
                int pc_s = rk_source % G.Pc;
                // Assuming gathered_AB_blocks was correctly filled by MPI_Gatherv
                const u32* block_ptr = &gathered_AB_blocks[rk_source * local_N_pts]; // This offset is only if gathered_AB_blocks stores blocks contiguously

                for(int lr = 0; lr < local_slice_rows; ++lr) {
                    int gr = pr_s * local_slice_rows + lr;
                    for(int lc = 0; lc < local_slice_cols; ++lc) {
                        int gc = pc_s * local_slice_cols + lc;
                        if (gr < N2_row_len && gc < N1_col_len) { 
                             res_final[gr * N1_col_len + gc] = block_ptr[lr * local_slice_cols + lc];
                        }
                    }
                }
            }
        }
        int output_len = 2 * n_poly -1;
        if (output_len < 0) output_len = 0;
        for(int i = 0; i < output_len; ++i) AB_coeff[i] = (i < lim) ? res_final[i] : 0;
        if (n_poly == 0 && output_len == -1) { /* handle no output needed */ }
         else if (output_len == 0 && n_poly > 0) { /* if 2n-1 is 0, like n=0.5? */ }
         else if (output_len == 0 && n_poly == 0) { AB_coeff[0] = 0; } // Or handle as no elements
    }
}

/* ----------------------------- main ------------------------------ */
static int a_in[300000], b_in[300000], ab_out[600000];

int main(int argc, char* argv[]){
    MPI_Init(&argc, &argv);
    Grid G = make_grid(MPI_COMM_WORLD);

    if(G.rank == 0)
        cout << "Six-step Barrett NTT (Rev 2 - LocalTranspose) | grid " << G.Pr << "×" << G.Pc << '\n';

    const int first = 0, last = 3;
    for(int id = first; id <= last; ++id){
        int n_poly_len=0, p_mod_val=0;
        if(G.rank == 0) {
            fRead(a_in, b_in, &n_poly_len, &p_mod_val, id);
        }
        
        MPI_Bcast(&n_poly_len, 1, MPI_INT, 0, G.world_comm);
        MPI_Bcast(&p_mod_val, 1, MPI_INT, 0, G.world_comm);

        if (n_poly_len > 0) {
            MPI_Bcast(a_in, n_poly_len, MPI_INT, 0, G.world_comm);
            MPI_Bcast(b_in, n_poly_len, MPI_INT, 0, G.world_comm);
        }

        MPI_Barrier(G.world_comm);
        double t0 = MPI_Wtime();

        if (n_poly_len > 0 && p_mod_val > 1) { 
            poly_multiply_six(a_in, b_in, ab_out, n_poly_len, p_mod_val, G);
        } else if (G.rank == 0) { 
             int len_to_clear = 2*n_poly_len-1;
             if (len_to_clear < 0 && n_poly_len == 0) len_to_clear = 0; // Expected for n=0 in test
             else if (len_to_clear < 0) len_to_clear = 0;

             for(int i=0; i < len_to_clear; ++i) ab_out[i] = 0;
        }

        MPI_Barrier(G.world_comm);
        double t1 = MPI_Wtime();

        if(G.rank == 0){
            if (n_poly_len > 0 && p_mod_val > 1) {
                 fCheck(ab_out, n_poly_len, id);
                 cout << "six-step latency n=" << n_poly_len << " p=" << p_mod_val
                      << " : " << (t1 - t0) * 1e6 << " us\n";
            } else {
                cout << "Skipped test id=" << id << " due to n_poly_len=" << n_poly_len << " or p_mod_val=" << p_mod_val << "\n";
                if (n_poly_len ==0) fCheck(ab_out, 0, id); 
            }
        }
    }
    MPI_Finalize();
    return 0;
}
