#include <iostream>
#include <vector>
using namespace std;

// Simple manual polynomial multiplication for verification
vector<int> manual_poly_mul(const vector<int>& a, const vector<int>& b, int p) {
    int n = a.size();
    vector<int> result(2 * n - 1, 0);
    
    for (int i = 0; i < n; i++) {
        for (int j = 0; j < n; j++) {
            result[i + j] = (result[i + j] + 1LL * a[i] * b[j]) % p;
        }
    }
    
    return result;
}

int main() {
    vector<int> a = {4, 1, 5, 2};
    vector<int> b = {1, 5, 5, 4};
    int p = 7340033;
    
    auto result = manual_poly_mul(a, b, p);
    
    cout << "Manual multiplication result: ";
    for (int x : result) {
        cout << x << " ";
    }
    cout << endl;
    
    // Check if p-1 is divisible by powers of 2
    int p_minus_1 = p - 1;
    int max_power_of_2 = 0;
    while (p_minus_1 % 2 == 0) {
        p_minus_1 /= 2;
        max_power_of_2++;
    }
    
    cout << "p-1 = " << (p-1) << endl;
    cout << "Max power of 2 that divides p-1: 2^" << max_power_of_2 << endl;
    cout << "This means we can do NTT up to size 2^" << max_power_of_2 << endl;
    
    return 0;
}
