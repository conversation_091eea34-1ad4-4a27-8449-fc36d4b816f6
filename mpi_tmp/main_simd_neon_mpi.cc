/**************************************************************
 *  main_neon_barrett.cc  ―― 修正版
 *    · 解决 m==1 时向量写越界 → munmap_chunk 崩溃
 *    · lanes<4 时退回标量蝶形，确保内存安全
 *    · 其余逻辑与之前版本一致（Barrett + NEON Radix‑4）
 *************************************************************/
#include <bits/stdc++.h>
#ifdef __ARM_NEON
#   include <arm_neon.h>
#endif

/* ============================== I/O 例程 ============================== */
void fRead(int *a,int *b,int *n,int *p,int id){
    std::string path="../nttdata/"+std::to_string(id)+".in";
    std::ifstream fin(path);
    fin>>*n>>*p;
    for(int i=0;i<*n;++i) fin>>a[i];
    for(int i=0;i<*n;++i) fin>>b[i];
}
void fCheck(int *ab,int n,int id){
    std::string path="../nttdata/"+std::to_string(id)+".out";
    std::ifstream fin(path);
    for(int i=0;i<2*n-1;++i){
        int x;fin>>x;
        if(x!=ab[i]){std::cout<<"多项式乘法结果错误\n";return;}
    }
    std::cout<<"多项式乘法结果正确\n";
}
void fWrite(int *ab,int n,int id){
    std::string path="files/"+std::to_string(id)+".out";
    std::ofstream fout(path);
    for(int i=0;i<2*n-1;++i) fout<<ab[i]<<'\n';
}

/* ============================== 工具函数 ============================== */
using uint32 = uint32_t; using uint64 = uint64_t;
static inline uint32 qpow(uint32 x,uint64 y,uint32 mod){
    uint64 res=1,base=x%mod;
    while(y){ if(y&1) res=res*base%mod; base=base*base%mod; y>>=1; }
    return uint32(res);
}
/* Barrett */
struct Barrett{
    uint32 mod; uint64 im;
    explicit Barrett(uint32 m):mod(m),im(~0ULL/m+1){}
    inline uint32 reduce(uint64 x)const{
        uint64 q=(__uint128_t)x*im>>64;
        uint32 r=uint32(x-q*mod);
        return r>=mod? r-mod:r;
    }
    inline uint32 mul(uint32 a,uint32 b)const{ return reduce(uint64(a)*b); }
};
/* bit‑reverse */
static void bitrev(std::vector<int>& r,int n){
    int lg=0; while((1<<lg)<n) ++lg;
    for(int i=0;i<n;++i){
        int v=0,x=i;
        for(int j=0;j<lg;++j){ v=(v<<1)|(x&1); x>>=1; }
        r[i]=v;
    }
}

/* ============================== 标量 NTT ============================== */
static void ntt_scalar(uint32* a,int n,bool inv,uint32 mod,uint32 g=3){
    std::vector<int> rev(n); bitrev(rev,n);
    for(int i=0;i<n;++i) if(i<rev[i]) std::swap(a[i],a[rev[i]]);
    for(int len=2;len<=n;len<<=1){
        uint32 wn=qpow(g,(mod-1)/len,mod);
        if(inv) wn=qpow(wn,mod-2,mod);
        int m=len>>1;
        for(int i=0;i<n;i+=len){
            uint32 w=1;
            for(int j=0;j<m;++j){
                uint32 u=a[i+j], v=uint64(a[i+j+m])*w%mod;
                a[i+j]=(u+v)%mod;
                a[i+j+m]=(u+mod-v)%mod;
                w=uint64(w)*wn%mod;
            }
        }
    }
    if(inv){
        uint32 invn=qpow(n,mod-2,mod);
        for(int i=0;i<n;++i) a[i]=uint64(a[i])*invn%mod;
    }
}

#ifdef __ARM_NEON
/* ======================== NEON Radix‑4 NTT (修正版) ======================== */
static void simdNTTRadix4(std::vector<uint32>& a,bool inv,
                           const Barrett& br,uint32 g=3){
    const int n=a.size();
    std::vector<int> rev(n); bitrev(rev,n);
    for(int i=0;i<n;++i) if(i<rev[i]) std::swap(a[i],a[rev[i]]);
    
    for(int len=4;len<=n;len<<=2){
        int m=len>>2;
        uint32 wn=qpow(g,(br.mod-1)/len,br.mod);
        if(inv) wn=qpow(wn,br.mod-2,br.mod);

        for(int off=0;off<n;off+=len){
            uint32 w1=1;
            for(int j=0;j<m;){
                if(m-j>=4){                     /* ---- 向量处理 4 lanes ---- */
                    uint32 tw1[4],tw2[4],tw3[4];
                    uint32 cur=w1;
                    for(int k=0;k<4;++k){
                        tw1[k]=cur;
                        tw2[k]=br.mul(cur,cur);
                        tw3[k]=br.mul(tw2[k],cur);
                        cur=br.mul(cur,wn);
                    }
                    w1=cur;

                    uint32x4_t va0=vld1q_u32(&a[off+j]);
                    uint32x4_t va1=vld1q_u32(&a[off+j+m]);
                    uint32x4_t va2=vld1q_u32(&a[off+j+2*m]);
                    uint32x4_t va3=vld1q_u32(&a[off+j+3*m]);

                    uint32 arr1[4],arr2[4],arr3[4];
                    vst1q_u32(arr1,va1); vst1q_u32(arr2,va2); vst1q_u32(arr3,va3);
                    for(int k=0;k<4;++k){
                        arr1[k]=br.mul(arr1[k],tw1[k]);
                        arr2[k]=br.mul(arr2[k],tw2[k]);
                        arr3[k]=br.mul(arr3[k],tw3[k]);
                    }
                    va1=vld1q_u32(arr1); va2=vld1q_u32(arr2); va3=vld1q_u32(arr3);

                    uint32x4_t t0=vaddq_u32(va0,va2);
                    uint32x4_t t1=vsubq_u32(va0,va2);
                    uint32x4_t t2=vaddq_u32(va1,va3);
                    uint32x4_t t3=vsubq_u32(va1,va3);

                    vst1q_u32(&a[off+j],        vaddq_u32(t0,t2));
                    vst1q_u32(&a[off+j+m],      vaddq_u32(t1,t3));
                    vst1q_u32(&a[off+j+2*m],    vsubq_u32(t0,t2));
                    vst1q_u32(&a[off+j+3*m],    vsubq_u32(t1,t3));
                    j+=4;
                }else{                         /* ---- 剩余 (<4) 标量蝶形 ---- */
                    uint32 tw1=w1;
                    uint32 tw2=br.mul(tw1,tw1);
                    uint32 tw3=br.mul(tw2,tw1);
                    uint32 a0=a[off+j];
                    uint32 a1=br.mul(a[off+j+m],tw1);
                    uint32 a2=br.mul(a[off+j+2*m],tw2);
                    uint32 a3=br.mul(a[off+j+3*m],tw3);

                    uint32 t0=(a0+a2)%br.mod;
                    uint32 t1=(a0+br.mod-a2)%br.mod;
                    uint32 t2=(a1+a3)%br.mod;
                    uint32 t3=(a1+br.mod-a3)%br.mod;

                    a[off+j]=(t0+t2)%br.mod;
                    a[off+j+m]=(t1+t3)%br.mod;
                    a[off+j+2*m]=(t0+br.mod-t2)%br.mod;
                    a[off+j+3*m]=(t1+br.mod-t3)%br.mod;

                    w1=br.mul(w1,wn);
                    ++j;
                }
            }
        }
    }
    if(inv){
        uint32 invn=qpow(n,br.mod-2,br.mod);
        for(auto &v:a) v=br.mul(v,invn);
    }
}
#endif /* __ARM_NEON */

/* ============================== NTT 选择器 ============================== */
static void ntt_auto(uint32* a,int n,bool inv,uint32 mod){
#if defined(__ARM_NEON)
    if(n>=16){
        std::vector<uint32> buf(a,a+n);
        simdNTTRadix4(buf,inv,Barrett(mod));
        for(int i=0;i<n;++i) a[i]=buf[i];
        return;
    }
#endif
    ntt_scalar(a,n,inv,mod);
}

/* ============================== 乘法封装 ============================== */
static void poly_multiply(int* A,int* B,int* C,int n,uint32 mod){
    int lim=1; while(lim<2*n) lim<<=1;
    std::vector<uint32> a(lim,0),b(lim,0);
    for(int i=0;i<n;++i){
        a[i]=uint32((A[i]%mod+mod)%mod);
        b[i]=uint32((B[i]%mod+mod)%mod);
    }
    ntt_auto(a.data(),lim,false,mod);
    ntt_auto(b.data(),lim,false,mod);
    for(int i=0;i<lim;++i) a[i]=uint64(a[i])*b[i]%mod;
    ntt_auto(a.data(),lim,true,mod);
    for(int i=0;i<2*n-1;++i) C[i]=int(a[i]);
}

/* ============================== 主程序 ============================== */
static int a[300000],b[300000],ab[300000];
int main(){
    for(int id=0;id<=3;++id){
        int n,p; fRead(a,b,&n,&p,id);
        memset(ab,0,sizeof(ab));
        auto st=std::chrono::high_resolution_clock::now();
        poly_multiply(a,b,ab,n,p);
        auto ed=std::chrono::high_resolution_clock::now();
        std::chrono::duration<double,std::milli> dt=ed-st;
        fCheck(ab,n,id);
        std::cout<<"n = "<<n<<", p = "<<p
                 <<", latency = "<<std::fixed<<std::setprecision(3)
                 <<dt.count()<<" ms\n";
    }
    return 0;
}
