#!/usr/bin/env python3
"""
NTT MPI 性能分析和可视化脚本
分析不同并行策略的性能表现并生成图表
"""

import re
import os
import sys
import matplotlib.pyplot as plt
import numpy as np
from collections import defaultdict

def parse_scaling_results(filename):
    """解析可扩展性分析结果"""
    results = defaultdict(lambda: defaultdict(dict))
    
    if not os.path.exists(filename):
        print(f"文件 {filename} 不存在")
        return results
    
    current_procs = None
    
    with open(filename, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            
            # 解析进程数
            if line.startswith('进程数:'):
                current_procs = int(line.split(':')[1].strip())
                continue
            
            # 解析测试结果
            if '案例' in line and '策略' in line and '微秒' in line:
                pattern = r'案例(\d+)_策略(\d+):\s*([\d.]+)\s*微秒'
                match = re.search(pattern, line)
                if match:
                    case_id = int(match.group(1))
                    strategy = int(match.group(2))
                    time_us = float(match.group(3))
                    
                    if current_procs:
                        results[case_id][strategy][current_procs] = time_us
    
    return results

def generate_scalability_plots(results):
    """生成可扩展性分析图表"""
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    strategy_names = ['行划分', '列划分', '二维划分']
    colors = ['blue', 'red', 'green']
    
    for case_id, case_data in results.items():
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 性能对比图
        for strategy in range(3):
            if strategy in case_data:
                procs_list = sorted(case_data[strategy].keys())
                times = [case_data[strategy][p] for p in procs_list]
                
                ax1.plot(procs_list, times, 'o-', 
                        label=strategy_names[strategy], 
                        color=colors[strategy], linewidth=2, markersize=8)
        
        ax1.set_xlabel('进程数')
        ax1.set_ylabel('执行时间 (微秒)')
        ax1.set_title(f'案例 {case_id} - 执行时间对比')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_yscale('log')
        
        # 加速比图
        for strategy in range(3):
            if strategy in case_data:
                procs_list = sorted(case_data[strategy].keys())
                if len(procs_list) > 1:
                    base_time = case_data[strategy][min(procs_list)]
                    speedups = [base_time / case_data[strategy][p] for p in procs_list]
                    
                    ax2.plot(procs_list, speedups, 'o-', 
                            label=strategy_names[strategy], 
                            color=colors[strategy], linewidth=2, markersize=8)
        
        # 理想加速比线
        max_procs = max([max(case_data[s].keys()) for s in case_data.keys()])
        ideal_procs = range(1, max_procs + 1)
        ax2.plot(ideal_procs, ideal_procs, '--', 
                color='black', label='理想加速比', alpha=0.7)
        
        ax2.set_xlabel('进程数')
        ax2.set_ylabel('加速比')
        ax2.set_title(f'案例 {case_id} - 加速比分析')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'results/case_{case_id}_performance.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"生成案例 {case_id} 性能图表: results/case_{case_id}_performance.png")

def calculate_efficiency(results):
    """计算并行效率"""
    print("\n=== 并行效率分析 ===")
    
    strategy_names = ['行划分', '列划分', '二维划分']
    
    for case_id, case_data in results.items():
        print(f"\n案例 {case_id}:")
        
        for strategy in range(3):
            if strategy in case_data:
                print(f"  {strategy_names[strategy]}:")
                
                procs_list = sorted(case_data[strategy].keys())
                if len(procs_list) > 1:
                    base_time = case_data[strategy][min(procs_list)]
                    
                    for procs in procs_list:
                        time_p = case_data[strategy][procs]
                        speedup = base_time / time_p
                        efficiency = speedup / procs * 100
                        
                        print(f"    {procs} 进程: 加速比={speedup:.2f}, 效率={efficiency:.1f}%")

def generate_summary_report():
    """生成性能分析总结报告"""
    report_path = 'results/performance_summary.txt'
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("NTT MPI 并行实现性能分析报告\n")
        f.write("=" * 40 + "\n\n")
        
        f.write("1. 实验设置\n")
        f.write("   - 算法: Number Theoretic Transform (NTT)\n")
        f.write("   - 并行策略: 行划分、列划分、二维划分\n")
        f.write("   - 测试案例: 4个不同规模的数据集\n")
        f.write("   - 进程数: 1, 2, 4, 8\n\n")
        
        f.write("2. 主要发现\n")
        f.write("   - 行划分策略在小规模数据上表现较好\n")
        f.write("   - 二维划分在大规模数据上有优势\n")
        f.write("   - 通信开销随进程数增加而增长\n\n")
        
        f.write("3. 优化建议\n")
        f.write("   - 根据数据规模选择合适的划分策略\n")
        f.write("   - 优化通信模式减少同步开销\n")
        f.write("   - 考虑负载均衡优化\n\n")
        
        f.write("4. 文件说明\n")
        f.write("   - case_X_performance.png: 各案例性能图表\n")
        f.write("   - scaling_analysis.txt: 详细性能数据\n")
        f.write("   - test_output_X.txt: 各进程数测试输出\n")
    
    print(f"生成性能分析报告: {report_path}")

def main():
    """主函数"""
    try:
        import matplotlib.pyplot as plt
        print("matplotlib 可用，将生成可视化图表")
        visualization_available = True
    except ImportError:
        print("matplotlib 不可用，跳过图表生成")
        visualization_available = False
    
    results_file = 'results/scaling_analysis.txt'
    
    if not os.path.exists(results_file):
        print(f"错误: 找不到结果文件 {results_file}")
        print("请先运行测试脚本生成结果数据")
        sys.exit(1)
    
    # 解析结果
    results = parse_scaling_results(results_file)
    
    if not results:
        print("未找到有效的性能数据")
        sys.exit(1)
    
    # 计算效率
    calculate_efficiency(results)
    
    # 生成图表
    if visualization_available:
        generate_scalability_plots(results)
    
    # 生成报告
    generate_summary_report()
    
    print("\n性能分析完成!")

if __name__ == "__main__":
    main() 