#!/bin/bash

echo "=== NTT MPI 并行实现测试脚本 ==="
echo "时间: $(date)"
echo "================================"

cd "$(dirname "$0")/.."

if [ ! -f "./ntt_mpi" ]; then
    echo "编译程序..."
    make clean
    make
fi

if [ ! -f "./ntt_mpi" ]; then
    echo "错误: 编译失败"
    exit 1
fi

echo "运行测试..."

echo ""
echo "=== 单进程测试 (串行对比) ==="
mpirun -n 1 ./ntt_mpi

echo ""
echo "=== 2进程测试 ==="
mpirun -n 2 ./ntt_mpi

echo ""
echo "=== 4进程测试 ==="
mpirun -n 4 ./ntt_mpi

echo ""
echo "=== 8进程测试 ==="
mpirun -n 8 ./ntt_mpi

echo ""
echo "=== 可扩展性分析 ==="
echo "进程数,策略,案例,时间(微秒)" > results/scalability_summary.csv

for procs in 1 2 4 8; do
    echo "测试 $procs 进程..."
    mpirun -n $procs ./ntt_mpi >> results/test_output_${procs}.txt 2>&1
done

echo ""
echo "=== 测试完成 ==="
echo "结果文件位于 results/ 目录:"
ls -la results/
echo ""
echo "查看可扩展性分析:"
if [ -f "results/scaling_analysis.txt" ]; then
    cat results/scaling_analysis.txt
fi 