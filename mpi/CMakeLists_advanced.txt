cmake_minimum_required(VERSION 3.16)
project(NTT_Advanced_Optimizations)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(MPI REQUIRED)
find_package(OpenMP REQUIRED)

include_directories(${CMAKE_CURRENT_SOURCE_DIR})

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O3 -march=native")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -ffast-math -funroll-loops")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra")

if("${CMAKE_CXX_COMPILER_ID}" STREQUAL "GNU")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fopenmp-simd")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -ftree-vectorize")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fopt-info-vec-optimized")
endif()

if("${CMAKE_CXX_COMPILER_ID}" STREQUAL "Clang")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fopenmp-simd")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Rpass=loop-vectorize")
endif()

if(CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64|arm64")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -mcpu=native")
    add_definitions(-D__ARM_NEON)
elseif(CMAKE_SYSTEM_PROCESSOR MATCHES "x86_64|amd64")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -mavx2 -mfma")
    add_definitions(-D__AVX2__)
endif()

find_library(NUMA_LIBRARY numa)
if(NUMA_LIBRARY)
    add_definitions(-DNUMA_SUPPORT)
    set(EXTRA_LIBS ${EXTRA_LIBS} ${NUMA_LIBRARY})
endif()

set(ADVANCED_SOURCES
    src/adaptive_precision_barrett.cpp
    src/async_comm_manager.cpp
    src/hybrid_parallel_ntt.cpp
)

set(ADVANCED_HEADERS
    ntt_advanced_optimizations.h
)

add_library(ntt_advanced ${ADVANCED_SOURCES})
target_link_libraries(ntt_advanced 
    MPI::MPI_CXX 
    OpenMP::OpenMP_CXX
    ${EXTRA_LIBS}
)

add_executable(test_advanced_optimizations test_advanced_optimizations.cpp)
target_link_libraries(test_advanced_optimizations 
    ntt_advanced
    MPI::MPI_CXX 
    OpenMP::OpenMP_CXX
    ${EXTRA_LIBS}
)

set_target_properties(test_advanced_optimizations PROPERTIES
    COMPILE_FLAGS "-DMPI_ENABLED -DOMP_ENABLED"
)

if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -fsanitize=address")
    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -fsanitize=address")
endif()

add_custom_target(benchmark
    COMMAND mpirun -np 4 ./test_advanced_optimizations
    DEPENDS test_advanced_optimizations
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
    COMMENT "运行高级优化基准测试"
)

add_custom_target(profile
    COMMAND perf record -g mpirun -np 4 ./test_advanced_optimizations
    COMMAND perf report
    DEPENDS test_advanced_optimizations
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
    COMMENT "运行性能分析"
) 