/**
 * @file hybrid_parallel_ntt.cpp
 * @brief 混合并行NTT实现 - MPI+OpenMP+SIMD三级并行
 * 
 * 创新优化点：
 * 1. 三级并行层次结构
 * 2. NUMA感知的线程绑定
 * 3. 动态负载均衡
 * 4. 缓存友好的数据分布
 * 5. 向量化友好的内存布局
 */

#include "ntt_advanced_optimizations.h"
#include <omp.h>
#include <sched.h>
#include <numa.h>
#include <immintrin.h>
#include <arm_neon.h>

namespace ntt_advanced {

HybridParallelNTT::HybridParallelNTT(MPI_Comm comm, int threads) 
    : mpi_comm_(comm), num_threads_(threads) {
    
    if (threads <= 0) {
        num_threads_ = omp_get_max_threads();
    }
    
    omp_set_num_threads(num_threads_);
    setup_numa_affinity();
}

void HybridParallelNTT::setup_numa_affinity() {
#ifdef NUMA_SUPPORT
    int rank;
    MPI_Comm_rank(mpi_comm_, &rank);
    
    int num_nodes = numa_num_configured_nodes();
    int node_id = rank % num_nodes;
    
    struct bitmask* node_mask = numa_allocate_nodemask();
    numa_bitmask_setbit(node_mask, node_id);
    numa_set_membind(node_mask);
    numa_free_nodemask(node_mask);
    
#pragma omp parallel
    {
        int thread_id = omp_get_thread_num();
        cpu_set_t cpu_set;
        CPU_ZERO(&cpu_set);
        
        int cores_per_node = numa_num_configured_cpus() / num_nodes;
        int base_cpu = node_id * cores_per_node;
        int target_cpu = base_cpu + (thread_id % cores_per_node);
        
        CPU_SET(target_cpu, &cpu_set);
        sched_setaffinity(0, sizeof(cpu_set), &cpu_set);
    }
#endif
}

class SIMDOptimizedButterfly {
public:
    static void butterfly_avx2(
        std::uint32_t* data, 
        size_t stride, 
        const std::uint32_t* twiddles,
        std::uint32_t mod,
        size_t count) {
        
#ifdef __AVX2__
        const __m256i mod_vec = _mm256_set1_epi32(static_cast<int>(mod));
        const size_t simd_count = count & ~7ULL;
        
        for (size_t i = 0; i < simd_count; i += 8) {
            __m256i a_vec = _mm256_loadu_si256(
                reinterpret_cast<const __m256i*>(&data[i])
            );
            __m256i b_vec = _mm256_loadu_si256(
                reinterpret_cast<const __m256i*>(&data[i + stride])
            );
            __m256i tw_vec = _mm256_loadu_si256(
                reinterpret_cast<const __m256i*>(&twiddles[i])
            );
            
            __m256i b_tw_lo = _mm256_mul_epu32(b_vec, tw_vec);
            __m256i b_tw_hi = _mm256_mul_epu32(
                _mm256_srli_epi64(b_vec, 32),
                _mm256_srli_epi64(tw_vec, 32)
            );
            
            __m256i b_tw = _mm256_blend_epi32(b_tw_lo, _mm256_slli_epi64(b_tw_hi, 32), 0xAA);
            
            __m256i sum = _mm256_add_epi32(a_vec, b_tw);
            __m256i diff = _mm256_sub_epi32(a_vec, b_tw);
            
            __m256i sum_cmp = _mm256_cmpgt_epi32(sum, mod_vec);
            __m256i diff_neg = _mm256_cmpgt_epi32(mod_vec, diff);
            
            sum = _mm256_sub_epi32(sum, _mm256_and_si256(sum_cmp, mod_vec));
            diff = _mm256_add_epi32(diff, _mm256_and_si256(diff_neg, mod_vec));
            
            _mm256_storeu_si256(reinterpret_cast<__m256i*>(&data[i]), sum);
            _mm256_storeu_si256(reinterpret_cast<__m256i*>(&data[i + stride]), diff);
        }
        
        for (size_t i = simd_count; i < count; ++i) {
            std::uint32_t a = data[i];
            std::uint64_t b_tw = static_cast<std::uint64_t>(data[i + stride]) * twiddles[i];
            std::uint32_t b_reduced = static_cast<std::uint32_t>(b_tw % mod);
            
            data[i] = (a + b_reduced) % mod;
            data[i + stride] = (a + mod - b_reduced) % mod;
        }
#endif
    }
    
    static void butterfly_neon(
        std::uint32_t* data,
        size_t stride,
        const std::uint32_t* twiddles,
        std::uint32_t mod,
        size_t count) {
        
#ifdef __ARM_NEON
        const uint32x4_t mod_vec = vdupq_n_u32(mod);
        const size_t neon_count = count & ~3ULL;
        
        for (size_t i = 0; i < neon_count; i += 4) {
            uint32x4_t a_vec = vld1q_u32(&data[i]);
            uint32x4_t b_vec = vld1q_u32(&data[i + stride]);
            uint32x4_t tw_vec = vld1q_u32(&twiddles[i]);
            
            uint64x2_t b_tw_lo = vmull_u32(vget_low_u32(b_vec), vget_low_u32(tw_vec));
            uint64x2_t b_tw_hi = vmull_u32(vget_high_u32(b_vec), vget_high_u32(tw_vec));
            
            uint32x2_t b_tw_lo_reduced = vmovn_u64(vdivq_u64(b_tw_lo, vmovl_u32(vget_low_u32(mod_vec))));
            uint32x2_t b_tw_hi_reduced = vmovn_u64(vdivq_u64(b_tw_hi, vmovl_u32(vget_high_u32(mod_vec))));
            
            uint32x4_t b_tw_reduced = vcombine_u32(b_tw_lo_reduced, b_tw_hi_reduced);
            
            uint32x4_t sum = vaddq_u32(a_vec, b_tw_reduced);
            uint32x4_t diff = vsubq_u32(a_vec, b_tw_reduced);
            
            uint32x4_t sum_cmp = vcgtq_u32(sum, mod_vec);
            uint32x4_t diff_neg = vcgtq_u32(mod_vec, diff);
            
            sum = vsubq_u32(sum, vandq_u32(sum_cmp, mod_vec));
            diff = vaddq_u32(diff, vandq_u32(diff_neg, mod_vec));
            
            vst1q_u32(&data[i], sum);
            vst1q_u32(&data[i + stride], diff);
        }
        
        for (size_t i = neon_count; i < count; ++i) {
            std::uint32_t a = data[i];
            std::uint64_t b_tw = static_cast<std::uint64_t>(data[i + stride]) * twiddles[i];
            std::uint32_t b_reduced = static_cast<std::uint32_t>(b_tw % mod);
            
            data[i] = (a + b_reduced) % mod;
            data[i + stride] = (a + mod - b_reduced) % mod;
        }
#endif
    }
};

void HybridParallelNTT::three_level_parallel_ntt(
    std::uint32_t* data,
    size_t length,
    bit inverse,
    std::uint32_t mod) {
    
    int rank, size;
    MPI_Comm_rank(mpi_comm_, &rank);
    MPI_Comm_size(mpi_comm_, &size);
    
    const size_t local_length = length / size;
    const size_t local_start = rank * local_length;
    
    std::vector<std::uint32_t> local_data(data + local_start, data + local_start + local_length);
    
    const AdaptivePrecisionBarrett<std::uint32_t> barrett(mod);
    
    for (size_t len = 2; len <= local_length; len <<= 1) {
        const size_t half = len >> 1;
        const std::uint32_t root = compute_primitive_root(mod, len);
        const std::uint32_t base_twiddle = inverse ? 
            barrett.reduce(static_cast<std::uint64_t>(mod - 1) / len) : 
            barrett.reduce(static_cast<std::uint64_t>(1));
        
#pragma omp parallel for schedule(dynamic, 64)
        for (size_t start = 0; start < local_length; start += len) {
            std::vector<std::uint32_t> twiddles(half);
            std::uint32_t w = 1;
            
            for (size_t j = 0; j < half; ++j) {
                twiddles[j] = w;
                w = barrett.reduce(static_cast<std::uint64_t>(w) * base_twiddle);
            }
            
#ifdef __AVX2__
            SIMDOptimizedButterfly::butterfly_avx2(
                &local_data[start], half, twiddles.data(), mod, half
            );
#elif defined(__ARM_NEON)
            SIMDOptimizedButterfly::butterfly_neon(
                &local_data[start], half, twiddles.data(), mod, half
            );
#else
            for (size_t j = 0; j < half; ++j) {
                std::uint32_t u = local_data[start + j];
                std::uint32_t v = barrett.reduce(
                    static_cast<std::uint64_t>(local_data[start + j + half]) * twiddles[j]
                );
                
                local_data[start + j] = barrett.reduce(static_cast<std::uint64_t>(u) + v);
                local_data[start + j + half] = barrett.reduce(static_cast<std::uint64_t>(u) + mod - v);
            }
#endif
        }
    }
    
    AsyncCommManager async_comm(mpi_comm_);
    
    if (size > 1) {
        std::vector<std::vector<std::uint32_t>> send_data(size);
        for (int dest = 0; dest < size; ++dest) {
            if (dest != rank) {
                send_data[dest] = local_data;
            }
        }
        
        std::vector<std::vector<std::uint32_t>> recv_data;
        async_comm.start_nonblocking_alltoall(send_data, recv_data);
        
        std::vector<std::uint32_t> global_data(length);
        for (int src = 0; src < size; ++src) {
            if (src == rank) {
                std::copy(local_data.begin(), local_data.end(), 
                         global_data.begin() + src * local_length);
            } else {
                std::copy(recv_data[src].begin(), recv_data[src].end(),
                         global_data.begin() + src * local_length);
            }
        }
        
        async_comm.wait_all();
        
        std::copy(global_data.begin(), global_data.end(), data);
    } else {
        std::copy(local_data.begin(), local_data.end(), data + local_start);
    }
}

void HybridParallelNTT::adaptive_parallel_ntt(
    std::uint32_t* data,
    size_t length,
    bool inverse,
    std::uint32_t mod) {
    
    int rank, size;
    MPI_Comm_rank(mpi_comm_, &rank);
    MPI_Comm_size(mpi_comm_, &size);
    
    const size_t threshold_large = 1024 * 1024;
    const size_t threshold_small = 1024;
    
    if (length >= threshold_large && size > 1) {
        three_level_parallel_ntt(data, length, inverse, mod);
    } else if (length >= threshold_small) {
        const size_t local_length = length / size;
        const size_t local_start = rank * local_length;
        
#pragma omp parallel for
        for (size_t i = local_start; i < local_start + local_length; ++i) {
            single_threaded_ntt_chunk(&data[i], 1, inverse, mod);
        }
    } else {
        if (rank == 0) {
            single_threaded_ntt_chunk(data, length, inverse, mod);
        }
        MPI_Bcast(data, static_cast<int>(length), MPI_UINT32_T, 0, mpi_comm_);
    }
}

void HybridParallelNTT::single_threaded_ntt_chunk(
    std::uint32_t* data,
    size_t length,
    bool inverse,
    std::uint32_t mod) const {
    
    if (length <= 1) return;
    
    const AdaptivePrecisionBarrett<std::uint32_t> barrett(mod);
    
    bit_reverse_permute(data, length);
    
    for (size_t len = 2; len <= length; len <<= 1) {
        const size_t half = len >> 1;
        const std::uint32_t root = compute_primitive_root(mod, len);
        std::uint32_t w = 1;
        
        for (size_t start = 0; start < length; start += len) {
            for (size_t j = 0; j < half; ++j) {
                std::uint32_t u = data[start + j];
                std::uint32_t v = barrett.reduce(
                    static_cast<std::uint64_t>(data[start + j + half]) * w
                );
                
                data[start + j] = barrett.reduce(static_cast<std::uint64_t>(u) + v);
                data[start + j + half] = barrett.reduce(static_cast<std::uint64_t>(u) + mod - v);
                
                w = barrett.reduce(static_cast<std::uint64_t>(w) * root);
            }
        }
    }
    
    if (inverse) {
        const std::uint32_t inv_n = barrett.reduce(mod - 1) / static_cast<std::uint32_t>(length);
        for (size_t i = 0; i < length; ++i) {
            data[i] = barrett.reduce(static_cast<std::uint64_t>(data[i]) * inv_n);
        }
    }
}

void HybridParallelNTT::bit_reverse_permute(std::uint32_t* data, size_t length) const {
    const size_t log_n = __builtin_ctzll(length);
    
#pragma omp parallel for
    for (size_t i = 0; i < length; ++i) {
        size_t rev = 0;
        size_t temp = i;
        for (size_t j = 0; j < log_n; ++j) {
            rev = (rev << 1) | (temp & 1);
            temp >>= 1;
        }
        
        if (i < rev) {
            std::swap(data[i], data[rev]);
        }
    }
}

std::uint32_t HybridParallelNTT::compute_primitive_root(std::uint32_t mod, size_t order) const {
    std::uint32_t g = 3;
    std::uint32_t exp = (mod - 1) / static_cast<std::uint32_t>(order);
    
    const AdaptivePrecisionBarrett<std::uint32_t> barrett(mod);
    
    std::uint32_t result = 1;
    while (exp > 0) {
        if (exp & 1) {
            result = barrett.reduce(static_cast<std::uint64_t>(result) * g);
        }
        g = barrett.reduce(static_cast<std::uint64_t>(g) * g);
        exp >>= 1;
    }
    
    return result;
}

class PerformanceProfiler {
private:
    struct ProfileData {
        std::chrono::high_resolution_clock::time_point start_time;
        double mpi_time = 0.0;
        double omp_time = 0.0;
        double simd_time = 0.0;
        size_t cache_misses = 0;
        size_t instructions = 0;
    };
    
    ProfileData current_profile_;
    
public:
    void start_profiling() {
        current_profile_.start_time = std::chrono::high_resolution_clock::now();
    }
    
    void add_mpi_time(double time) { current_profile_.mpi_time += time; }
    void add_omp_time(double time) { current_profile_.omp_time += time; }
    void add_simd_time(double time) { current_profile_.simd_time += time; }
    
    void print_profile_summary() const {
        auto total_time = std::chrono::duration_cast<std::chrono::microseconds>(
            std::chrono::high_resolution_clock::now() - current_profile_.start_time
        ).count() / 1000.0;
        
        std::cout << "=== Performance Profile ===\n";
        std::cout << "Total time: " << total_time << " ms\n";
        std::cout << "MPI time: " << current_profile_.mpi_time << " ms (" 
                 << (current_profile_.mpi_time / total_time * 100) << "%)\n";
        std::cout << "OpenMP time: " << current_profile_.omp_time << " ms (" 
                 << (current_profile_.omp_time / total_time * 100) << "%)\n";
        std::cout << "SIMD time: " << current_profile_.simd_time << " ms (" 
                 << (current_profile_.simd_time / total_time * 100) << "%)\n";
    }
};

} // namespace ntt_advanced 