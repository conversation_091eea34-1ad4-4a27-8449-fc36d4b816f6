#include "../include/ntt_mpi.h"
#ifdef __ARM_NEON
#include <arm_neon.h>
#endif
#include <vector>
#include <cstdint>
#include <algorithm>
#include <cmath>
#include <cassert>

namespace ntt_mpi {

/**
 * @brief Barrett快速模运算器构造函数
 * @param m 模数
 */
BarrettReducer::BarrettReducer(uint64_t m):mod(m){inv=((__uint128_t(1)<<64)/m);} 

/**
 * @brief Barrett规约操作
 * @param x 输入值
 * @return 规约后的值
 */
uint32_t BarrettReducer::reduce(uint64_t x) const {
    uint64_t q=((__uint128_t)x*inv)>>64;
    uint64_t r=x-q*mod;
    if(r>=mod) r-=mod;
    return static_cast<uint32_t>(r);
}

/**
 * @brief Barrett乘法
 * @param a 第一个数
 * @param b 第二个数
 * @return a*b mod p
 */
uint32_t BarrettReducer::mul(uint32_t a,uint32_t b) const {
    return reduce(static_cast<uint64_t>(a)*b);
}

/**
 * @brief Radix-4串行NTT
 * @param a 输入数组
 * @param inverse 是否逆变换
 * @param br Barrett规约器
 */
void serialNTTRadix4(std::vector<uint32_t>& a,bool inverse,const BarrettReducer& br){
    int n=a.size();
    std::vector<int> rev(n);
    computeBitReverse(rev,n);
    for(int i=0;i<n;++i) if(i<rev[i]) std::swap(a[i],a[rev[i]]);
    for(int len=4;len<=n;len<<=2){
        int m=len>>2;
        uint32_t wn=quickPow(3,(br.mod-1)/len,br.mod);
        if(inverse) wn=quickPow(wn,br.mod-2,br.mod);
        for(int i=0;i<n;i+=len){
            uint32_t w1=1;
            uint32_t w2=br.mul(w1,w1);
            uint32_t w3=br.mul(w2,w1);
            for(int j=0;j<m;++j){
                uint32_t a0=a[i+j];
                uint32_t a1=br.mul(a[i+j+m],w1);
                uint32_t a2=br.mul(a[i+j+2*m],w2);
                uint32_t a3=br.mul(a[i+j+3*m],w3);
                uint32_t t0=(a0+a2)%br.mod;
                uint32_t t1=(a0+br.mod-a2)%br.mod;
                uint32_t t2=(a1+a3)%br.mod;
                uint32_t t3=( (uint64_t)(a1+br.mod-a3)%br.mod );
                a[i+j]=(t0+t2)%br.mod;
                a[i+j+m]=br.mul((t1+t3)%br.mod,wn);
                a[i+j+2*m]=(t0+br.mod-t2)%br.mod;
                a[i+j+3*m]=br.mul((t1+br.mod-t3)%br.mod,wn);
                w1=br.mul(w1,wn);
                w2=br.mul(w1,w1);
                w3=br.mul(w2,w1);
            }
        }
    }
    if(inverse){
        uint32_t inv_n=quickPow(n,br.mod-2,br.mod);
        for(uint32_t &v:a) v=br.mul(v,inv_n);
    }
}

/**
 * @brief Radix-8串行NTT
 * @param a 输入数组
 * @param inverse 是否逆变换
 * @param br Barrett规约器
 */
void serialNTTRadix8(std::vector<uint32_t>& a,bool inverse,const BarrettReducer& br){
    int n=a.size();
    std::vector<int> rev(n);
    computeBitReverse(rev,n);
    for(int i=0;i<n;++i) if(i<rev[i]) std::swap(a[i],a[rev[i]]);
    
    for(int len=8;len<=n;len<<=3){
        int m=len>>3;
        uint32_t wn=quickPow(3,(br.mod-1)/len,br.mod);
        if(inverse) wn=quickPow(wn,br.mod-2,br.mod);
        
        for(int i=0;i<n;i+=len){
            uint32_t w[8];
            w[0]=1;
            for(int k=1;k<8;++k) w[k]=br.mul(w[k-1],wn);
            
            for(int j=0;j<m;++j){
                uint32_t x[8];
                for(int k=0;k<8;++k) x[k]=br.mul(a[i+j+k*m],w[k]);
                
                uint32_t t[8];
                t[0]=(x[0]+x[4])%br.mod;
                t[1]=(x[0]+br.mod-x[4])%br.mod;
                t[2]=(x[2]+x[6])%br.mod; 
                t[3]=(x[2]+br.mod-x[6])%br.mod;
                t[4]=(x[1]+x[5])%br.mod;
                t[5]=(x[1]+br.mod-x[5])%br.mod;
                t[6]=(x[3]+x[7])%br.mod;
                t[7]=(x[3]+br.mod-x[7])%br.mod;
                
                a[i+j+0*m]=(t[0]+t[2]+t[4]+t[6])%br.mod;
                a[i+j+1*m]=(t[0]+t[3]+br.mul(t[5],wn)+br.mul(t[6],wn))%br.mod;
                a[i+j+2*m]=(t[0]+br.mod-t[2]+br.mul(t[4],wn)+br.mul(t[6],wn))%br.mod;
                a[i+j+3*m]=(t[0]+br.mod-t[3]+br.mul(t[5],wn)+br.mul(t[6],wn))%br.mod;
                a[i+j+4*m]=(t[1]+t[2]+br.mul(t[4],wn)+br.mul(t[7],wn))%br.mod;
                a[i+j+5*m]=(t[1]+t[3]+br.mul(t[5],wn)+br.mul(t[7],wn))%br.mod;
                a[i+j+6*m]=(t[1]+br.mod-t[2]+br.mul(t[4],wn)+br.mul(t[7],wn))%br.mod;
                a[i+j+7*m]=(t[1]+br.mod-t[3]+br.mul(t[5],wn)+br.mul(t[7],wn))%br.mod;
            }
        }
    }
    
    if(inverse){
        uint32_t inv_n=quickPow(n,br.mod-2,br.mod);
        for(uint32_t &v:a) v=br.mul(v,inv_n);
    }
}

/**
 * @brief 三模CRT合并算法
 * @param r0 模p0的结果
 * @param r1 模p1的结果  
 * @param r2 模p2的结果
 * @param p0 第一个模数
 * @param p1 第二个模数
 * @param p2 第三个模数
 * @param out 合并后的64位结果
 */
void crtMergeThree(const std::vector<uint32_t>& r0,const std::vector<uint32_t>& r1,
                   const std::vector<uint32_t>& r2,uint32_t p0,uint32_t p1,uint32_t p2,
                   std::vector<uint64_t>& out){
    int n=r0.size();
    out.resize(n);
    uint64_t m01=uint64_t(p0)*p1;
    uint64_t inv_p0_p1=quickPow(p0,p1-2,p1);
    uint64_t inv_m01_p2=quickPow(m01%p2,p2-2,p2);
    for(int i=0;i<n;++i){
        uint64_t x=r0[i];
        uint64_t t=((r1[i]+p1-x%p1)%p1)*inv_p0_p1%p1;
        x+=t*uint64_t(p0);
        uint64_t t2=((r2[i] + p2 - x%p2)%p2)*inv_m01_p2%p2;
        x+=t2*m01;
        out[i]=x;
    }
}

#ifdef __ARM_NEON
/**
 * @brief SIMD优化的Radix-4 NTT
 * @param a 输入数组
 * @param inverse 是否逆变换
 * @param br Barrett规约器
 */
void simdNTTRadix4(std::vector<uint32_t>& a,bool inverse,const BarrettReducer& br){
    int n=a.size();
    std::vector<int> rev(n);
    computeBitReverse(rev,n);
    for(int i=0;i<n;++i) if(i<rev[i]) std::swap(a[i],a[rev[i]]);
    
    for(int len=4;len<=n;len<<=2){
        int m=len>>2;
        uint32_t wn=quickPow(3,(br.mod-1)/len,br.mod);
        if(inverse) wn=quickPow(wn,br.mod-2,br.mod);
        
        for(int i=0;i<n;i+=len){
            uint32_t w1=1,w2=br.mul(w1,w1),w3=br.mul(w2,w1);
            for(int j=0;j+3<m;j+=4){
                uint32x4_t va0=vld1q_u32(&a[i+j]);
                uint32x4_t va1=vld1q_u32(&a[i+j+m]);
                uint32x4_t va2=vld1q_u32(&a[i+j+2*m]);
                uint32x4_t va3=vld1q_u32(&a[i+j+3*m]);
                
                uint32x4_t vt0=vaddq_u32(va0,va2);
                uint32x4_t vt1=vsubq_u32(va0,va2);
                uint32x4_t vt2=vaddq_u32(va1,va3);
                uint32x4_t vt3=vsubq_u32(va1,va3);
                
                vst1q_u32(&a[i+j],vaddq_u32(vt0,vt2));
                vst1q_u32(&a[i+j+m],vaddq_u32(vt1,vt3));
                vst1q_u32(&a[i+j+2*m],vsubq_u32(vt0,vt2));
                vst1q_u32(&a[i+j+3*m],vsubq_u32(vt1,vt3));
                
                w1=br.mul(w1,wn);w2=br.mul(w1,w1);w3=br.mul(w2,w1);
            }
            for(int j=(m&~3);j<m;++j){
                uint32_t a0=a[i+j],a1=br.mul(a[i+j+m],w1);
                uint32_t a2=br.mul(a[i+j+2*m],w2),a3=br.mul(a[i+j+3*m],w3);
                uint32_t t0=(a0+a2)%br.mod,t1=(a0+br.mod-a2)%br.mod;
                uint32_t t2=(a1+a3)%br.mod,t3=(a1+br.mod-a3)%br.mod;
                a[i+j]=(t0+t2)%br.mod;
                a[i+j+m]=(t1+t3)%br.mod;
                a[i+j+2*m]=(t0+br.mod-t2)%br.mod;
                a[i+j+3*m]=(t1+br.mod-t3)%br.mod;
                w1=br.mul(w1,wn);w2=br.mul(w1,w1);w3=br.mul(w2,w1);
            }
        }
    }
    
    if(inverse){
        uint32_t inv_n=quickPow(n,br.mod-2,br.mod);
        for(uint32_t &v:a) v=br.mul(v,inv_n);
    }
}
#endif

/**
 * @brief 高级MPI分层并行NTT实现
 * @param data 输入数据
 * @param n 数据长度
 * @param inverse 是否逆变换
 * @param config NTT配置
 * @param ctx MPI上下文
 */
void advancedMPINTT(std::vector<int>& data, int n, bool inverse,
                   const NTTConfig& config, const MPIContext& ctx) {
    
    std::vector<int> local_data;
    int local_n = n / ctx.size;
    local_data.resize(local_n);
    
    MPI_Scatter(data.data(), local_n, MPI_INT, 
                local_data.data(), local_n, MPI_INT, 0, ctx.comm);
    
    std::vector<int> local_rev(local_n);
    for (int i = 0; i < local_n; ++i) {
        int global_i = ctx.rank * local_n + i;
        local_rev[i] = 0;
        int temp = global_i;
        for (int j = 0; j < __builtin_ctz(n); ++j) {
            local_rev[i] = (local_rev[i] << 1) | (temp & 1);
            temp >>= 1;
        }
    }
    
    for (int i = 0; i < local_n; ++i) {
        int target_rank = local_rev[i] / local_n;
        int target_idx = local_rev[i] % local_n;
        
        if (target_rank != ctx.rank) {
            int send_data = local_data[i];
            int recv_data;
            
            if (ctx.rank < target_rank) {
                MPI_Send(&send_data, 1, MPI_INT, target_rank, i, ctx.comm);
                MPI_Recv(&recv_data, 1, MPI_INT, target_rank, target_idx, ctx.comm, MPI_STATUS_IGNORE);
            } else {
                MPI_Recv(&recv_data, 1, MPI_INT, target_rank, target_idx, ctx.comm, MPI_STATUS_IGNORE);
                MPI_Send(&send_data, 1, MPI_INT, target_rank, i, ctx.comm);
            }
            local_data[i] = recv_data;
        }
    }
    
    int log_n = __builtin_ctz(n);
    
    for (int level = 0; level < log_n; ++level) {
        int len = 1 << (level + 1);
        int m = len >> 1;
        
        uint64_t wn = quickPow(config.g, (config.p - 1) / len, config.p);
        if (inverse) wn = quickPow(wn, config.p - 2, config.p);
        
        std::vector<int> temp_data = local_data;
        
        for (int i = 0; i < local_n; ++i) {
            int global_i = ctx.rank * local_n + i;
            int pos = global_i % len;
            
            if (pos < m) {
                int partner_global = global_i + m;
                int partner_rank = partner_global / local_n;
                int partner_local = partner_global % local_n;
                
                uint64_t w = quickPow(wn, pos, config.p);
                
                if (partner_rank == ctx.rank) {
                    int u = local_data[i];
                    int v = (1LL * local_data[partner_local] * w) % config.p;
                    temp_data[i] = (u + v) % config.p;
                    temp_data[partner_local] = (u - v + config.p) % config.p;
                } else {
                    int partner_val;
                    MPI_Sendrecv(&local_data[i], 1, MPI_INT, partner_rank, level,
                               &partner_val, 1, MPI_INT, partner_rank, level,
                               ctx.comm, MPI_STATUS_IGNORE);
                    
                    int u = local_data[i];
                    int v = (1LL * partner_val * w) % config.p;
                    temp_data[i] = (u + v) % config.p;
                }
            }
        }
        
        local_data = temp_data;
        MPI_Barrier(ctx.comm);
        
        std::vector<int> global_temp(n);
        MPI_Allgather(local_data.data(), local_n, MPI_INT,
                     global_temp.data(), local_n, MPI_INT, ctx.comm);
        
        for (int i = 0; i < local_n; ++i) {
            local_data[i] = global_temp[ctx.rank * local_n + i];
        }
    }
    
    if (inverse) {
        uint64_t inv_n = quickPow(n, config.p - 2, config.p);
        for (int i = 0; i < local_n; ++i) {
            local_data[i] = (1LL * local_data[i] * inv_n) % config.p;
        }
    }
    
    MPI_Gather(local_data.data(), local_n, MPI_INT,
               data.data(), local_n, MPI_INT, 0, ctx.comm);
    MPI_Bcast(data.data(), n, MPI_INT, 0, ctx.comm);
}

/**
 * @brief 多模CRT并行多项式乘法
 * @param a 多项式A
 * @param b 多项式B
 * @param result 结果
 * @param n 长度
 * @param ctx MPI上下文
 */
void multiModularMPIMultiply(const std::vector<int>& a, const std::vector<int>& b,
                            std::vector<uint64_t>& result, int n, const MPIContext& ctx) {
    
    uint32_t p0 = 998244353;
    uint32_t p1 = 1004535809;
    uint32_t p2 = 469762049;
    
    std::vector<uint32_t> r0(n), r1(n), r2(n);
    
    int mod_per_proc = 3 / ctx.size;
    int my_start_mod = ctx.rank * mod_per_proc;
    int my_end_mod = std::min(my_start_mod + mod_per_proc, 3);
    
    for (int mod_idx = my_start_mod; mod_idx < my_end_mod; ++mod_idx) {
        uint32_t p = (mod_idx == 0) ? p0 : (mod_idx == 1) ? p1 : p2;
        BarrettReducer br(p);
        
        std::vector<uint32_t> A(n), B(n);
        for (int i = 0; i < n; ++i) {
            A[i] = a[i] % p;
            B[i] = b[i] % p;
        }
        
        int lim = 1;
        while (lim < 2 * n) lim <<= 1;
        A.resize(lim, 0);
        B.resize(lim, 0);
        
        if (ctx.size >= 4) {
            serialNTTRadix8(A, false, br);
            serialNTTRadix8(B, false, br);
        } else {
            serialNTTRadix4(A, false, br);
            serialNTTRadix4(B, false, br);
        }
        
        for (int i = 0; i < lim; ++i) {
            A[i] = br.mul(A[i], B[i]);
        }
        
        if (ctx.size >= 4) {
            serialNTTRadix8(A, true, br);
        } else {
            serialNTTRadix4(A, true, br);
        }
        
        if (mod_idx == 0) {
            for (int i = 0; i < n; ++i) r0[i] = A[i];
        } else if (mod_idx == 1) {
            for (int i = 0; i < n; ++i) r1[i] = A[i];
        } else {
            for (int i = 0; i < n; ++i) r2[i] = A[i];
        }
    }
    
    MPI_Allreduce(MPI_IN_PLACE, r0.data(), n, MPI_UINT32_T, MPI_SUM, ctx.comm);
    MPI_Allreduce(MPI_IN_PLACE, r1.data(), n, MPI_UINT32_T, MPI_SUM, ctx.comm);
    MPI_Allreduce(MPI_IN_PLACE, r2.data(), n, MPI_UINT32_T, MPI_SUM, ctx.comm);
    
    if (ctx.rank == 0) {
        crtMergeThree(r0, r1, r2, p0, p1, p2, result);
    }
    
    int result_size = result.size();
    MPI_Bcast(&result_size, 1, MPI_INT, 0, ctx.comm);
    if (ctx.rank != 0) result.resize(result_size);
    MPI_Bcast(result.data(), result_size * sizeof(uint64_t), MPI_BYTE, 0, ctx.comm);
}

/**
 * @brief 混合并行策略：数据+任务并行
 * @param data 输入数据
 * @param n 数据长度
 * @param inverse 是否逆变换
 * @param config NTT配置
 * @param ctx MPI上下文
 */
void hybridParallelNTT(std::vector<int>& data, int n, bool inverse,
                      const NTTConfig& config, const MPIContext& ctx) {
    if (ctx.size == 1) {
        serialNTT(data, n, inverse, config.p, config.g);
        return;
    }
    
    if (n <= 1024 || ctx.size <= 2) {
        nttRowPartition(data, n, inverse, config, ctx);
    } else if (ctx.size == 4) {
        nttColumnPartition(data, n, inverse, config, ctx);
    } else {
        ntt2DPartition(data, n, inverse, config, ctx);
    }
}

} // namespace ntt_mpi 