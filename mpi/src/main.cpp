/**
 * @file main.cpp
 * @brief NTT MPI并行实现的主程序
 * @details 测试不同并行策略的性能，与原始main.cc保持一致的测试逻辑
 */

#include "../include/ntt_mpi.h"
#include <iostream>
#include <chrono>
#include <fstream>
#include <iomanip>
#include <vector>
#include <string>

using namespace ntt_mpi;

void updateNTTConfig(NTTConfig& config, int p) {
    config.p = p;
    config.g = 3;
    
    switch (p) {
        case 7340033:
        case 104857601:
        case 469762049:
            config.g = 3;
            break;
        default:
            config.g = 3;
            break;
    }
}

void testStrategy(int strategy, const std::string& strategy_name, 
                 const MPIContext& ctx) {
    if (ctx.rank == 0) {
        std::cout << "\n=== Testing Strategy: " << strategy_name << " ===" << std::endl;
    }
    int test_begin = 0;
    int test_end = 3;
    
    for (int i = test_begin; i <= test_end; ++i) {
        std::vector<int> a, b, result;
        int n, p;
        
        if (ctx.rank == 0) {
            readData(a, b, &n, &p, i);
        }
        
        MPI_Bcast(&n, 1, MPI_INT, 0, ctx.comm);
        MPI_Bcast(&p, 1, MPI_INT, 0, ctx.comm);
        
        if (ctx.rank != 0) {
            a.resize(n);
            b.resize(n);
        }
        
        MPI_Bcast(a.data(), n, MPI_INT, 0, ctx.comm);
        MPI_Bcast(b.data(), n, MPI_INT, 0, ctx.comm);
        
        NTTConfig config;
        config.n = n;
        config.strategy = strategy;
        updateNTTConfig(config, p);
        
        MPI_Barrier(ctx.comm);
        auto start = std::chrono::high_resolution_clock::now();
        
        polynomialMultiply(a, b, result, n, config, ctx);
        
        MPI_Barrier(ctx.comm);
        auto end = std::chrono::high_resolution_clock::now();
        
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        double elapsed_time = duration.count();
        
        if (ctx.rank == 0) {
            checkResult(result, n, i);
            writeResult(result, n, i);
            std::cout << "Test case " << i << " - n=" << n << ", p=" << p 
                     << " - Time: " << std::fixed << std::setprecision(2) 
                     << elapsed_time << " microseconds" << std::endl;
        }
    }
}

void performanceComparison(const MPIContext& ctx) {
    if (ctx.rank == 0) {
        std::cout << "\n=== Performance Comparison ===" << std::endl;
        std::cout << "Number of Processes: " << ctx.size << std::endl;
    }
    
    std::vector<std::string> strategy_names = {"Row Partition", "Column Partition", "2D Partition"};
    
    for (int strategy = 0; strategy < 3; ++strategy) {
        testStrategy(strategy, strategy_names[strategy], ctx);
    }
}

void scalabilityAnalysis(const MPIContext& ctx) {
    if (ctx.rank == 0) {
        std::cout << "\n=== Scalability Analysis ===" << std::endl;
        std::cout << "Current Number of Processes: " << ctx.size << std::endl;
        
        std::ofstream scaling_file("results/scaling_analysis.txt", std::ios::app);
        scaling_file << "Number of Processes: " << ctx.size << std::endl;
        scaling_file.close();
    }
    
    std::vector<int> test_cases = {1, 2, 3};
    
    for (int test_case : test_cases) {
        std::vector<int> a, b, result;
        int n, p;
        
        if (ctx.rank == 0) {
            readData(a, b, &n, &p, test_case);
        }
        
        MPI_Bcast(&n, 1, MPI_INT, 0, ctx.comm);
        MPI_Bcast(&p, 1, MPI_INT, 0, ctx.comm);
        
        if (ctx.rank != 0) {
            a.resize(n);
            b.resize(n);
        }
        
        MPI_Bcast(a.data(), n, MPI_INT, 0, ctx.comm);
        MPI_Bcast(b.data(), n, MPI_INT, 0, ctx.comm);
        
        for (int strategy = 0; strategy < 3; ++strategy) {
            NTTConfig config;
            config.n = n;
            config.strategy = strategy;
            updateNTTConfig(config, p);
            
            double total_time = 0;
            int num_runs = 3;
            
            for (int run = 0; run < num_runs; ++run) {
                MPI_Barrier(ctx.comm);
                auto start = std::chrono::high_resolution_clock::now();
                
                polynomialMultiply(a, b, result, n, config, ctx);
                
                MPI_Barrier(ctx.comm);
                auto end = std::chrono::high_resolution_clock::now();
                
                auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
                total_time += duration.count();
            }
            
            double avg_time = total_time / num_runs;
            
            if (ctx.rank == 0) {
                std::cout << "Test Case " << test_case << " Strategy " << strategy 
                         << " Average Time: " << std::fixed << std::setprecision(2) 
                         << avg_time << " microseconds" << std::endl;
                
                std::ofstream scaling_file("results/scaling_analysis.txt", std::ios::app);
                scaling_file << "Test Case " << test_case << "_Strategy " << strategy 
                           << ": " << avg_time << " microseconds" << std::endl;
                scaling_file.close();
            }
        }
    }
}

void communicationAnalysis(const MPIContext& ctx) {
    if (ctx.rank == 0) {
        std::cout << "\n=== Communication Overhead Analysis ===" << std::endl;
    }
    
    std::vector<int> a, b, result;
    int n = 131072, p = 469762049;
    
    if (ctx.rank == 0) {
        a.resize(n, 1);
        b.resize(n, 2);
        for (int i = 0; i < n; ++i) {
            a[i] = i % p;
            b[i] = (i * 2) % p;
        }
    } else {
        a.resize(n);
        b.resize(n);
    }
    
    MPI_Bcast(a.data(), n, MPI_INT, 0, ctx.comm);
    MPI_Bcast(b.data(), n, MPI_INT, 0, ctx.comm);
    
    for (int strategy = 0; strategy < 3; ++strategy) {
        NTTConfig config;
        config.n = n;
        config.strategy = strategy;
        config.p = p;
        config.g = 3;
        
        MPI_Barrier(ctx.comm);
        auto comm_start = std::chrono::high_resolution_clock::now();
        
        auto comp_start = std::chrono::high_resolution_clock::now();
        polynomialMultiply(a, b, result, n, config, ctx);
        auto comp_end = std::chrono::high_resolution_clock::now();
        
        MPI_Barrier(ctx.comm);
        auto comm_end = std::chrono::high_resolution_clock::now();
        
        auto total_time = std::chrono::duration_cast<std::chrono::microseconds>(comm_end - comm_start);
        auto comp_time = std::chrono::duration_cast<std::chrono::microseconds>(comp_end - comp_start);
        
        if (ctx.rank == 0) {
            double comm_overhead = (total_time.count() - comp_time.count()) / (double)total_time.count() * 100;
            std::cout << "Strategy " << strategy << " Communication Overhead Ratio: " 
                     << std::fixed << std::setprecision(2) << comm_overhead << "%" << std::endl;
        }
    }
}

void advancedFeatureTests(const MPIContext& ctx) {
    if (ctx.rank == 0) {
        std::cout << "\n=== Advanced Feature Tests ===" << std::endl;
    }
    // 测试参数
    std::vector<int> test_sizes{1024, 4096};
    std::vector<uint32_t> test_primes{998244353, 1004535809};

    for (int n : test_sizes) {
        for (uint32_t p : test_primes) {
            // 生成测试多项式
            std::vector<int> a(n), b(n);
            if (ctx.rank == 0) {
                for (int i = 0; i < n; ++i) {
                    a[i] = (i * 123 + 7) % p;
                    b[i] = (i * 456 + 11) % p;
                }
            }
            MPI_Bcast(a.data(), n, MPI_INT, 0, ctx.comm);
            MPI_Bcast(b.data(), n, MPI_INT, 0, ctx.comm);

            // Barrett+Radix-4
            {
                NTTConfig cfg{n, p, 3, 0, 4, true};
                std::vector<int> c;
                auto st = std::chrono::high_resolution_clock::now();
                polynomialMultiply(a, b, c, n, cfg, ctx);
                auto ed = std::chrono::high_resolution_clock::now();
                if (ctx.rank == 0) {
                    auto us = std::chrono::duration_cast<std::chrono::microseconds>(ed - st).count();
                    std::cout << "Radix-4+Barrett n=" << n << " p=" << p << " => " << us << " us" << std::endl;
                }
            }
            // Barrett+Radix-8
            {
                NTTConfig cfg{n, p, 3, 0, 8, true};
                std::vector<int> c;
                auto st = std::chrono::high_resolution_clock::now();
                polynomialMultiply(a, b, c, n, cfg, ctx);
                auto ed = std::chrono::high_resolution_clock::now();
                if (ctx.rank == 0) {
                    auto us = std::chrono::duration_cast<std::chrono::microseconds>(ed - st).count();
                    std::cout << "Radix-8+Barrett n=" << n << " p=" << p << " => " << us << " us" << std::endl;
                }
            }
            // 高级MPI分层NTT
            {
                NTTConfig cfg{n, p, 3, 0, 2, false};
                std::vector<int> data = a;
                auto st = std::chrono::high_resolution_clock::now();
                advancedMPINTT(data, n, false, cfg, ctx);
                advancedMPINTT(data, n, true, cfg, ctx);
                auto ed = std::chrono::high_resolution_clock::now();
                if (ctx.rank == 0) {
                    auto us = std::chrono::duration_cast<std::chrono::microseconds>(ed - st).count();
                    std::cout << "advancedMPINTT n=" << n << " p=" << p << " => " << us << " us" << std::endl;
                }
            }
            // 混合并行策略
            {
                NTTConfig cfg{n, p, 3, 1, 4, true};
                std::vector<int> data = a;
                auto st = std::chrono::high_resolution_clock::now();
                hybridParallelNTT(data, n, false, cfg, ctx);
                hybridParallelNTT(data, n, true, cfg, ctx);
                auto ed = std::chrono::high_resolution_clock::now();
                if (ctx.rank == 0) {
                    auto us = std::chrono::duration_cast<std::chrono::microseconds>(ed - st).count();
                    std::cout << "HybridParallel n=" << n << " p=" << p << " => " << us << " us" << std::endl;
                }
            }
        }
    }

    // 多模CRT测试
    int n = 4096;
    std::vector<int> a(n), b(n);
    if (ctx.rank == 0) {
        for (int i = 0; i < n; ++i) { a[i] = i % 100000; b[i] = (i * 3) % 100000; }
    }
    MPI_Bcast(a.data(), n, MPI_INT, 0, ctx.comm);
    MPI_Bcast(b.data(), n, MPI_INT, 0, ctx.comm);
    std::vector<uint64_t> res;
    auto st = std::chrono::high_resolution_clock::now();
    multiModularMPIMultiply(a, b, res, n, ctx);
    auto ed = std::chrono::high_resolution_clock::now();
    if (ctx.rank == 0) {
        auto us = std::chrono::duration_cast<std::chrono::microseconds>(ed - st).count();
        std::cout << "multiModularMPIMultiply n=" << n << " => " << us << " us" << std::endl;
    }
}

int main(int argc, char* argv[]) {
    MPIContext ctx = initializeMPI(&argc, &argv);
    
    if (ctx.rank == 0) {
        std::cout << "=== NTT MPI Parallel Implementation Test ===" << std::endl;
        std::cout << "Number of Processes: " << ctx.size << std::endl;
        std::cout << "==============================" << std::endl;
    }
    
    performanceComparison(ctx);
    
    scalabilityAnalysis(ctx);
    
    communicationAnalysis(ctx);

    advancedFeatureTests(ctx);
    
    if (ctx.rank == 0) {
        std::cout << "\n=== Test Completed ===" << std::endl;
        std::cout << "Result files have been saved to the results/ directory" << std::endl;
        std::cout << "Scalability analysis results are saved in results/scaling_analysis.txt" << std::endl;
    }
    
    finalizeMPI();
    return 0;
} 