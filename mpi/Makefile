CXX = mpicxx
CXXFLAGS = -std=c++17 -O3 -Wall -Wextra -Iinclude
TARGET = ntt_mpi
ADVANCED_TARGET = ntt_advanced_test
SRCDIR = src
OBJDIR = obj
SOURCES = $(wildcard $(SRCDIR)/*.cpp)
OBJECTS = $(SOURCES:$(SRCDIR)/%.cpp=$(OBJDIR)/%.o)

# 主程序对象文件
MAIN_OBJECTS = $(OBJDIR)/main.o $(OBJDIR)/ntt_mpi.o $(OBJDIR)/ntt_advanced.o
# 高级测试对象文件 (现在已合并到main中)
ADVANCED_OBJECTS = $(OBJDIR)/main.o $(OBJDIR)/ntt_mpi.o $(OBJDIR)/ntt_advanced.o

.PHONY: all clean run test advanced

all: $(TARGET) $(ADVANCED_TARGET)

$(TARGET): $(MAIN_OBJECTS) | results
	$(CXX) $(MAIN_OBJECTS) -o $@ $(CXXFLAGS)

$(ADVANCED_TARGET): $(ADVANCED_OBJECTS) | results
	$(CXX) $(ADVANCED_OBJECTS) -o $@ $(CXXFLAGS)

$(OBJDIR)/%.o: $(SRCDIR)/%.cpp | $(OBJDIR)
	$(CXX) $(CXXFLAGS) -c $< -o $@

$(OBJDIR):
	mkdir -p $(OBJDIR)

results:
	mkdir -p results

clean:
	rm -rf $(OBJDIR) $(TARGET) $(ADVANCED_TARGET) results/*

test: $(TARGET)
	mpirun -n 4 ./$(TARGET)

advanced: $(ADVANCED_TARGET)
	mpirun -n 4 ./$(ADVANCED_TARGET)

run: $(TARGET)
	mpirun -n 2 ./$(TARGET)

benchmark: $(TARGET)
	@echo "Running 2 processes..."
	mpirun -n 2 ./$(TARGET)
	@echo "Running 4 processes..."
	mpirun -n 4 ./$(TARGET)
	@echo "Running 8 processes..."
	mpirun -n 8 ./$(TARGET)

help:
	@echo "Available targets:"
	@echo "  all       - Build the project"
	@echo "  clean     - Remove build files"
	@echo "  run       - Run with 2 processes"
	@echo "  test      - Run with 4 processes"
	@echo "  benchmark - Run scalability tests" 