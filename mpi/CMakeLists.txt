cmake_minimum_required(VERSION 3.10)
project(NTT_MPI)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(MPI REQUIRED)

if(MPI_FOUND)
    include_directories(${MPI_INCLUDE_PATH})
endif()

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O3 -Wall -Wextra")

include_directories(include)

file(GLOB SOURCES "src/*.cpp")

add_executable(ntt_mpi ${SOURCES})

if(MPI_FOUND)
    target_link_libraries(ntt_mpi ${MPI_LIBRARIES})
    
    if(MPI_COMPILE_FLAGS)
        set_target_properties(ntt_mpi PROPERTIES
            COMPILE_FLAGS "${MPI_COMPILE_FLAGS}")
    endif()
    
    if(MPI_LINK_FLAGS)
        set_target_properties(ntt_mpi PROPERTIES
            LINK_FLAGS "${MPI_LINK_FLAGS}")
    endif()
endif()

set_target_properties(ntt_mpi PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin")

file(MAKE_DIRECTORY "${CMAKE_BINARY_DIR}/results")

install(TARGETS ntt_mpi DESTINATION bin) 